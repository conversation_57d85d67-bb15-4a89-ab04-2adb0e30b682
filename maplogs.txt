flutter: 📍 🔧 Added custom skin for pin 2545: pin-skin-2545
flutter: 📍 🔧 Updated real pin data for: 2545
flutter: 📍 🔧 Processing feature: 2524
flutter: 📍 🔧 Added custom skin for pin 2516: pin-skin-2516
flutter: 📍 🔧 Updated real pin data for: 2516
flutter: 📍 🔧 Processing feature: 2546
flutter: 📍 🔧 Added custom skin for pin 2546: pin-skin-2546
flutter: 📍 🔧 Updated real pin data for: 2546
flutter: 📍 🔧 Processing feature: 2530
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🔧 Added custom skin for pin 2545: pin-skin-2545
flutter: 📍 🔧 Updated real pin data for: 2545
flutter: 📍 🔧 Processing feature: 2524
flutter: 📍 🔧 Added custom skin for pin 2520: pin-skin-2520
flutter: 📍 🔧 Updated real pin data for: 2520
flutter: 📍 🔧 Processing feature: 2515
flutter: 📍 🔧 Added custom skin for pin 2534: pin-skin-2534
flutter: 📍 🔧 Updated real pin data for: 2534
flutter: 📍 🔧 Processing feature: 2537
flutter: 📍 🔧 Added custom skin for pin 2516: pin-skin-2516
flutter: 📍 🔧 Updated real pin data for: 2516
flutter: 📍 🔧 Processing feature: 2546
flutter: 📍 🔧 Added custom skin for pin 2533: pin-skin-2533
flutter: 📍 🔧 Updated real pin data for: 2533
flutter: 📍 🔧 Processing feature: 2532
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 📍 🔧 Added custom skin for pin 2546: pin-skin-2546
flutter: 📍 🔧 Updated real pin data for: 2546
flutter: 📍 🔧 Processing feature: 2530
flutter: 📍 🔧 Added custom skin for pin 2530: pin-skin-2530
flutter: 📍 🔧 Updated real pin data for: 2530
flutter: 📍 ✅ Added 5 pins to individual layer (total: 5)
flutter: 📍 ✅ Processed pin IDs: [2554, 2519, 2516, 2546, 2530]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 72)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 36)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 36)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Individual layer updated with 5 new pins
flutter: 📍 🧠 IMMEDIATE: Building cluster layer with 5 pins
flutter: 📍 🧠 IMMEDIATE: Calculated 5 clusters
flutter: 📍 🧠 IMMEDIATE: Updated cluster-pins-source with 5 features
flutter: 📍 🎯 FORCE: Updating cluster symbols (bypassing optimization)
flutter: 📍 🎯 FORCE: Created 0 cluster symbols for tap handling
flutter: 📍 ✅ Cluster layer updated with 5 total pins
flutter: 📍 🔄 IMMEDIATE: Layer visibility set - individual: false, cluster: true
flutter: 📍 ✅ Layer visibility set: cluster (zoom: 12.378399462679809)
flutter: 📍 ⚡ Batch processed - skipping layer visibility switch during progressive rendering (5 pins added, 5 total)
flutter: 📍 ⚡ Target layer: clusters (will be set when progressive rendering completes)
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 🎨 ✨ Progressive entrance animation for pin 2554 at (LatLng(36.146715900109825, -86.80797125212284))
flutter: 🎨 🌟 Creating progressive glow for pin 2554
flutter: 🎨 ✨ Pin 2554 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2519 at (LatLng(36.14830323857787, -86.80630297698838))
flutter: 🎨 🌟 Creating progressive glow for pin 2519
flutter: 🎨 ✨ Pin 2519 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2517: pin-skin-2517
flutter: 📍 🔧 Updated real pin data for: 2517
flutter: 📍 🔧 Processing feature: 2539
flutter: 🎨 ✨ Progressive entrance animation for pin 2516 at (LatLng(36.14589078721732, -86.81202035551155))
flutter: 🎨 🌟 Creating progressive glow for pin 2516
flutter: 🎨 ✨ Pin 2516 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2546 at (LatLng(36.14599900015739, -86.812285085892))
flutter: 🎨 🌟 Creating progressive glow for pin 2546
flutter: 🎨 ✨ Pin 2546 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2530 at (LatLng(36.1496013025832, -86.81330423691946))
flutter: 🎨 🌟 Creating progressive glow for pin 2530
flutter: 🎨 ✨ Pin 2530 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2515: pin-skin-2515
flutter: 📍 🔧 Updated real pin data for: 2515
flutter: 📍 🔧 Processing feature: 2536
flutter: 📍 🔧 Added custom skin for pin 2537: pin-skin-2537
flutter: 📍 🔧 Updated real pin data for: 2537
flutter: 📍 🔧 Processing feature: 2548
flutter: 📍 🔧 Added custom skin for pin 2517: pin-skin-2517
flutter: 📍 🔧 Updated real pin data for: 2517
flutter: 📍 🔧 Processing feature: 2539
flutter: 📍 🔧 Added custom skin for pin 2546: pin-skin-2546
flutter: 📍 🔧 Updated real pin data for: 2546
flutter: 📍 🔧 Processing feature: 2530
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 Pin state change: Pins: 0→5, Clusters: 0→0
flutter: 📍 ✅ State validation passed
flutter: 📍 🔧 Added custom skin for pin 2532: pin-skin-2532
flutter: 📍 🔧 Updated real pin data for: 2532
flutter: 📍 🔧 Processing feature: 2549
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 📍 🔧 Added custom skin for pin 2530: pin-skin-2530
flutter: 📍 🔧 Updated real pin data for: 2530
flutter: 📍 ✅ Added 5 pins to individual layer (total: 10)
flutter: 📍 ✅ Processed pin IDs: [2554, 2519, 2516, 2546, 2530]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 82)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 41)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 41)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Individual layer updated with 5 new pins
flutter: 📍 🧠 IMMEDIATE: Building cluster layer with 5 pins
flutter: 📍 🧠 IMMEDIATE: Calculated 5 clusters
flutter: 📍 🧠 IMMEDIATE: Updated cluster-pins-source with 5 features
flutter: 📍 🎯 FORCE: Updating cluster symbols (bypassing optimization)
flutter: 📍 🎯 FORCE: Created 0 cluster symbols for tap handling
flutter: 📍 ✅ Cluster layer updated with 5 total pins
flutter: 📍 🔄 IMMEDIATE: Layer visibility set - individual: false, cluster: true
flutter: 📍 ✅ Layer visibility set: cluster (zoom: 12.378399462679809)
flutter: 📍 ⚡ Batch processed - skipping layer visibility switch during progressive rendering (5 pins added, 5 total)
flutter: 📍 ⚡ Target layer: clusters (will be set when progressive rendering completes)
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 🎨 ✨ Progressive entrance animation for pin 2554 at (LatLng(36.146715900109825, -86.80797125212284))
flutter: 🎨 🌟 Creating progressive glow for pin 2554
flutter: 🎨 ✨ Pin 2554 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2519 at (LatLng(36.14830323857787, -86.80630297698838))
flutter: 🎨 🌟 Creating progressive glow for pin 2519
flutter: 🎨 ✨ Pin 2519 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2516 at (LatLng(36.14589078721732, -86.81202035551155))
flutter: 🎨 🌟 Creating progressive glow for pin 2516
flutter: 🎨 ✨ Pin 2516 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2546 at (LatLng(36.14599900015739, -86.812285085892))
flutter: 🎨 🌟 Creating progressive glow for pin 2546
flutter: 🎨 ✨ Pin 2546 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2539: pin-skin-2539
flutter: 📍 🔧 Updated real pin data for: 2539
flutter: 📍 🔧 Processing feature: 2542
flutter: 🎨 ✨ Progressive entrance animation for pin 2530 at (LatLng(36.1496013025832, -86.81330423691946))
flutter: 🎨 🌟 Creating progressive glow for pin 2530
flutter: 🎨 ✨ Pin 2530 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2524: pin-skin-2524
flutter: 📍 🔧 Updated real pin data for: 2524
flutter: 📍 🔧 Processing feature: 2538
flutter: 📍 🔧 Added custom skin for pin 2536: pin-skin-2536
flutter: 📍 🔧 Updated real pin data for: 2536
flutter: 📍 ✅ Added 5 pins to individual layer (total: 15)
flutter: 📍 ✅ Processed pin IDs: [2518, 2541, 2520, 2515, 2536]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 92)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 46)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 46)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Individual layer updated with 5 new pins
flutter: 📍 🧠 IMMEDIATE: Building cluster layer with 5 pins
flutter: 📍 🧠 IMMEDIATE: Calculated 3 clusters
flutter: 🎯 Added 1 new cluster icons: {}
flutter: 📍 🧠 IMMEDIATE: Added cluster icons for counts: {3}
flutter: 📍 🧠 IMMEDIATE: Updated cluster-pins-source with 3 features
flutter: 📍 🎯 FORCE: Updating cluster symbols (bypassing optimization)
flutter: 📍 🎯 FORCE: Created 1 cluster symbols for tap handling
flutter: 📍 ✅ Cluster layer updated with 5 total pins
flutter: 📍 🔄 IMMEDIATE: Layer visibility set - individual: false, cluster: true
flutter: 📍 ✅ Layer visibility set: cluster (zoom: 12.378399462679809)
flutter: 📍 ⚡ Batch processed - skipping layer visibility switch during progressive rendering (5 pins added, 10 total)
flutter: 📍 ⚡ Target layer: clusters (will be set when progressive rendering completes)
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 🎨 ✨ Progressive entrance animation for pin 2518 at (LatLng(36.13639322433334, -86.80075336483836))
flutter: 🎨 🌟 Creating progressive glow for pin 2518
flutter: 🎨 ✨ Pin 2518 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2541 at (LatLng(36.13639743322167, -86.80068876797904))
flutter: 🎨 🌟 Creating progressive glow for pin 2541
flutter: 🎨 ✨ Pin 2541 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2548: pin-skin-2548
flutter: 📍 🔧 Updated real pin data for: 2548
flutter: 🎨 ✨ Progressive entrance animation for pin 2520 at (LatLng(36.13663678478132, -86.79983681245955))
flutter: 🎨 🌟 Creating progressive glow for pin 2520
flutter: 🎨 ✨ Pin 2520 loaded with gentle animation
flutter: 📍 ✅ Added 5 pins to individual layer (total: 20)
flutter: 📍 ✅ Processed pin IDs: [2535, 2543, 2534, 2537, 2548]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 102)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 51)
flutter: 🎨 ✨ Progressive entrance animation for pin 2515 at (LatLng(36.13942714868769, -86.82215765064659))
flutter: 🎨 🌟 Creating progressive glow for pin 2515
flutter: 🎨 ✨ Pin 2515 loaded with gentle animation
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 51)
flutter: 📍 🔧 Added custom skin for pin 2539: pin-skin-2539
flutter: 📍 🔧 Updated real pin data for: 2539
flutter: 📍 🔧 Processing feature: 2542
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Individual layer updated with 5 new pins
flutter: 📍 🧠 IMMEDIATE: Building cluster layer with 5 pins
flutter: 📍 🧠 IMMEDIATE: Calculated 5 clusters
flutter: 📍 🔧 Added custom skin for pin 2530: pin-skin-2530
flutter: 📍 🔧 Updated real pin data for: 2530
flutter: 📍 🔧 Processing feature: 2521
flutter: 📍 🧠 IMMEDIATE: Updated cluster-pins-source with 5 features
flutter: 📍 🎯 FORCE: Updating cluster symbols (bypassing optimization)
flutter: 📍 🎯 FORCE: Created 0 cluster symbols for tap handling
flutter: 📍 ✅ Cluster layer updated with 5 total pins
flutter: 📍 🔄 IMMEDIATE: Layer visibility set - individual: false, cluster: true
flutter: 📍 ✅ Layer visibility set: cluster (zoom: 12.378399462679809)
flutter: 📍 ⚡ Batch processed - skipping layer visibility switch during progressive rendering (5 pins added, 15 total)
flutter: 📍 ⚡ Target layer: clusters (will be set when progressive rendering completes)
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 🎨 ✨ Progressive entrance animation for pin 2535 at (LatLng(36.152505813970336, -86.78867934665051))
flutter: 🎨 🌟 Creating progressive glow for pin 2535
flutter: 🎨 ✨ Pin 2535 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2536 at (LatLng(36.15134012615302, -86.78904405076455))
flutter: 🎨 🌟 Creating progressive glow for pin 2536
flutter: 🎨 ✨ Pin 2536 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2543 at (LatLng(36.13227342750656, -86.7958371883285))
flutter: 🎨 🌟 Creating progressive glow for pin 2543
flutter: 🎨 ✨ Pin 2543 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2549: pin-skin-2549
flutter: 📍 🔧 Updated real pin data for: 2549
flutter: 📍 🔧 Processing feature: 2522
flutter: 🗺️ LocationManager: Received foreground position update: lat: 36.14674128244283, lng: -86.80780084778819, accuracy: 22.464103243160956m, speed: 0.0m/s
flutter: 🗺️ LocationManager: Rejected low accuracy GPS (22.5m > 20.0m) (total filtered: 6)
flutter: 📍 Received position update: lat: 36.14674128244283, lng: -86.80780084778819, accuracy: 22.464103243160956m, speed: 0.0m/s
flutter: 📍 User avatar image already added, skipping
flutter: 📍 SmartFetch: Handling location update at 36.14674128244283, -86.80780084778819
flutter: 📍 SmartFetch: No fetch needed - using cached data
flutter: 🎨 ✨ Progressive entrance animation for pin 2534 at (LatLng(36.15839378466391, -86.78411582985878))
flutter: 🎨 🌟 Creating progressive glow for pin 2534
flutter: 🎨 ✨ Pin 2534 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2524: pin-skin-2524
flutter: 📍 🔧 Updated real pin data for: 2524
flutter: 📍 🔧 Processing feature: 2538
flutter: 🎨 ✨ Progressive entrance animation for pin 2537 at (LatLng(36.16209839572221, -86.78316809429695))
flutter: 🎨 🌟 Creating progressive glow for pin 2537
flutter: 🎨 ✨ Pin 2537 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2548 at (LatLng(36.16213141079441, -86.78292214581955))
flutter: 🎨 🌟 Creating progressive glow for pin 2548
flutter: 🎨 ✨ Pin 2548 loaded with gentle animation
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 📍 🔧 Added custom skin for pin 2542: pin-skin-2542
flutter: 📍 🔧 Updated real pin data for: 2542
flutter: 📍 ✅ Added 5 pins to individual layer (total: 25)
flutter: 📍 ✅ Processed pin IDs: [2527, 2544, 2517, 2539, 2542]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 112)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 56)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 56)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Individual layer updated with 5 new pins
flutter: 📍 🧠 IMMEDIATE: Building cluster layer with 5 pins
flutter: 📍 🧠 IMMEDIATE: Calculated 5 clusters
flutter: 📍 🧠 IMMEDIATE: Updated cluster-pins-source with 5 features
flutter: 📍 🎯 FORCE: Updating cluster symbols (bypassing optimization)
flutter: 📍 🎯 FORCE: Created 0 cluster symbols for tap handling
flutter: 📍 ✅ Cluster layer updated with 5 total pins
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🔄 IMMEDIATE: Layer visibility set - individual: false, cluster: true
flutter: 📍 ✅ Layer visibility set: cluster (zoom: 12.378421497617916)
flutter: 📍 ⚡ Batch processed - skipping layer visibility switch during progressive rendering (5 pins added, 20 total)
flutter: 📍 ⚡ Target layer: clusters (will be set when progressive rendering completes)
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 🎨 ✨ Progressive entrance animation for pin 2527 at (LatLng(36.13859156285574, -86.80594456092405))
flutter: 🎨 🌟 Creating progressive glow for pin 2527
flutter: 🎨 ✨ Pin 2527 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2544 at (LatLng(36.15660669930431, -86.8078186481036))
flutter: 🎨 🌟 Creating progressive glow for pin 2544
flutter: 🎨 ✨ Pin 2544 loaded with gentle animation
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 🎨 ✨ Progressive entrance animation for pin 2517 at (LatLng(36.15670067756571, -86.80775116011816))
flutter: 🎨 🌟 Creating progressive glow for pin 2517
flutter: 🎨 ✨ Pin 2517 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2539 at (LatLng(36.15100082630611, -86.79597599028992))
flutter: 🎨 🌟 Creating progressive glow for pin 2539
flutter: 🎨 ✨ Pin 2539 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2542 at (LatLng(36.15295341448084, -86.7970888270228))
flutter: 🎨 🌟 Creating progressive glow for pin 2542
flutter: 🎨 ✨ Pin 2542 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2538: pin-skin-2538
flutter: 📍 🔧 Updated real pin data for: 2538
flutter: 📍 🔧 Processing feature: 2526
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 Pin state change: Pins: 5→25, Clusters: 0→0
flutter: 📍 ✅ State validation passed
flutter: 📍 🔧 Added custom skin for pin 2542: pin-skin-2542
flutter: 📍 🔧 Updated real pin data for: 2542
flutter: 📍 ✅ Added 5 pins to individual layer (total: 30)
flutter: 📍 ✅ Processed pin IDs: [2527, 2544, 2517, 2539, 2542]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 122)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 61)
flutter: 📍 🔧 Added custom skin for pin 2521: pin-skin-2521
flutter: 📍 🔧 Updated real pin data for: 2521
flutter: 📍 🔧 Processing feature: 2545
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 61)
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Individual layer updated with 5 new pins
flutter: 📍 🧠 IMMEDIATE: Building cluster layer with 5 pins
flutter: 📍 🧠 IMMEDIATE: Calculated 5 clusters
flutter: 📍 🧠 IMMEDIATE: Updated cluster-pins-source with 5 features
flutter: 📍 🎯 FORCE: Updating cluster symbols (bypassing optimization)
flutter: 📍 🎯 FORCE: Created 0 cluster symbols for tap handling
flutter: 📍 ✅ Cluster layer updated with 5 total pins
flutter: 📍 🔄 IMMEDIATE: Layer visibility set - individual: false, cluster: true
flutter: 📍 ✅ Layer visibility set: cluster (zoom: 12.378421497617916)
flutter: 📍 ⚡ Batch processed - skipping layer visibility switch during progressive rendering (5 pins added, 20 total)
flutter: 📍 ⚡ Target layer: clusters (will be set when progressive rendering completes)
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 🎨 ✨ Progressive entrance animation for pin 2527 at (LatLng(36.13859156285574, -86.80594456092405))
flutter: 🎨 🌟 Creating progressive glow for pin 2527
flutter: 🎨 ✨ Pin 2527 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2538: pin-skin-2538
flutter: 📍 🔧 Updated real pin data for: 2538
flutter: 📍 🔧 Processing feature: 2526
flutter: 🎨 ✨ Progressive entrance animation for pin 2544 at (LatLng(36.15660669930431, -86.8078186481036))
flutter: 🎨 🌟 Creating progressive glow for pin 2544
flutter: 🎨 ✨ Pin 2544 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2517 at (LatLng(36.15670067756571, -86.80775116011816))
flutter: 🎨 🌟 Creating progressive glow for pin 2517
flutter: 🎨 ✨ Pin 2517 loaded with gentle animation
flutter: 📍 ⚠️ Progressive rendering active - deferring layer switch until completion
flutter: 📍 ⚠️ Current state: shouldCluster=true, currentlyShowingClusters=true
flutter: 📍 ⚠️ Debug state: _isPinFetchInProgress=true, timerActive=false, pendingPins=0
flutter: 🎨 ✨ Progressive entrance animation for pin 2539 at (LatLng(36.15100082630611, -86.79597599028992))
flutter: 🎨 🌟 Creating progressive glow for pin 2539
flutter: 🎨 ✨ Pin 2539 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2526: pin-skin-2526
flutter: 📍 🔧 Updated real pin data for: 2526
flutter: 📍 ✅ Added 5 pins to individual layer (total: 35)
flutter: 📍 ✅ Processed pin IDs: [2521, 2545, 2524, 2538, 2526]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 132)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 66)
flutter: 🎨 ✨ Progressive entrance animation for pin 2542 at (LatLng(36.15295341448084, -86.7970888270228))
flutter: 🎨 🌟 Creating progressive glow for pin 2542
flutter: 🎨 ✨ Pin 2542 loaded with gentle animation
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 66)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Individual layer updated with 5 new pins
flutter: 📍 🧠 IMMEDIATE: Building cluster layer with 5 pins
flutter: 📍 🧠 IMMEDIATE: Calculated 3 clusters
flutter: 📍 🧠 IMMEDIATE: Added cluster icons for counts: {3}
flutter: 📍 🧠 IMMEDIATE: Updated cluster-pins-source with 3 features
flutter: 📍 🎯 FORCE: Updating cluster symbols (bypassing optimization)
flutter: 📍 🎯 FORCE: Created 1 cluster symbols for tap handling
flutter: 📍 ✅ Cluster layer updated with 5 total pins
flutter: 📍 🔄 IMMEDIATE: Layer visibility set - individual: false, cluster: true
flutter: 📍 ✅ Layer visibility set: cluster (zoom: 12.378421497617916)
flutter: 📍 ⚡ Batch processed - skipping layer visibility switch during progressive rendering (5 pins added, 25 total)
flutter: 📍 ⚡ Target layer: clusters (will be set when progressive rendering completes)
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 🎨 ✨ Progressive entrance animation for pin 2521 at (LatLng(36.14308080228975, -86.80263351036342))
flutter: 🎨 🌟 Creating progressive glow for pin 2521
flutter: 🎨 ✨ Pin 2521 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2545 at (LatLng(36.14314842515128, -86.80249254618307))
flutter: 🎨 🌟 Creating progressive glow for pin 2545
flutter: 🎨 ✨ Pin 2545 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2524 at (LatLng(36.14469771341506, -86.80132024513051))
flutter: 🎨 🌟 Creating progressive glow for pin 2524
flutter: 🎨 ✨ Pin 2524 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2538 at (LatLng(36.15057003132827, -86.80188732191188))
flutter: 🎨 🌟 Creating progressive glow for pin 2538
flutter: 🎨 ✨ Pin 2538 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2526 at (LatLng(36.14572782526517, -86.80017581369405))
flutter: 🎨 🌟 Creating progressive glow for pin 2526
flutter: 🎨 ✨ Pin 2526 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2522: pin-skin-2522
flutter: 📍 🔧 Updated real pin data for: 2522
flutter: 📍 ✅ Added 5 pins to individual layer (total: 40)
flutter: 📍 ✅ Processed pin IDs: [2547, 2533, 2532, 2549, 2522]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 142)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 71)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 71)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Individual layer updated with 5 new pins
flutter: 📍 🧠 IMMEDIATE: Building cluster layer with 5 pins
flutter: 📍 🧠 IMMEDIATE: Calculated 3 clusters
flutter: 📍 🧠 IMMEDIATE: Added cluster icons for counts: {3}
flutter: 📍 🧠 IMMEDIATE: Updated cluster-pins-source with 3 features
flutter: 📍 🎯 FORCE: Updating cluster symbols (bypassing optimization)
flutter: 📍 🎯 FORCE: Created 1 cluster symbols for tap handling
flutter: 📍 ✅ Cluster layer updated with 5 total pins
flutter: 📍 🔄 IMMEDIATE: Layer visibility set - individual: false, cluster: true
flutter: 📍 ✅ Layer visibility set: cluster (zoom: 12.378421497617916)
flutter: 📍 ⚡ Batch processed - skipping layer visibility switch during progressive rendering (5 pins added, 30 total)
flutter: 📍 ⚡ Target layer: clusters (will be set when progressive rendering completes)
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 🎨 ✨ Progressive entrance animation for pin 2547 at (LatLng(36.158351444330904, -86.7761343461274))
flutter: 🎨 🌟 Creating progressive glow for pin 2547
flutter: 🎨 ✨ Pin 2547 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2533 at (LatLng(36.15748890906799, -86.77474918058346))
flutter: 🎨 🌟 Creating progressive glow for pin 2533
flutter: 🎨 ✨ Pin 2533 loaded with gentle animation
flutter: 📍 🔧 Added custom skin for pin 2526: pin-skin-2526
flutter: 📍 🔧 Updated real pin data for: 2526
flutter: 📍 ✅ Added 5 pins to individual layer (total: 45)
flutter: 📍 ✅ Processed pin IDs: [2521, 2545, 2524, 2538, 2526]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 152)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 76)
flutter: 🎨 ✨ Progressive entrance animation for pin 2532 at (LatLng(36.16543180815286, -86.78056411322653))
flutter: 🎨 🌟 Creating progressive glow for pin 2532
flutter: 🎨 ✨ Pin 2532 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 2549 at (LatLng(36.15991897374022, -86.77589148966442))
flutter: 🎨 🌟 Creating progressive glow for pin 2549
flutter: 🎨 ✨ Pin 2549 loaded with gentle animation
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 76)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:confetti/confetti.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:async';
import 'dart:math' as math;
import 'dart:ui';
import 'dart:convert';
import 'dart:io';

import '../../config/constants.dart';
import '../../theme/app_colors.dart';

import '../../providers/user_provider.dart';
import '../../providers/skin_provider.dart';
import '../../providers/gamification_provider.dart';
import '../../services/music/spotify_service.dart';
import '../../services/music/spotify_genre_service.dart';
import '../../services/music/lastfm_service.dart';
import '../../models/music_track.dart';
import '../ar/ar_pin_placement_screen.dart';
import '../map/map_screen.dart';
import 'ai_dialogue_content.dart';
import 'ai_onboarding_auth_service.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/profile/components/artist_card.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/artist_with_genre.dart';
import '../../utils/cycling_loader_state.dart';
import '../../services/music/genre_cycling_artist_loader.dart';
import '../../services/music/artist_cycling_song_loader.dart';
import '../../utils/infinite_scroll_controller.dart';
import '../../widgets/common/enhanced_loading_card.dart';
import '../../utils/onboarding_error_handler.dart';
import '../settings/terms_of_service_screen.dart';
import '../settings/privacy_policy_screen.dart';

class AIOnboardingScreen extends StatefulWidget {
  const AIOnboardingScreen({super.key});

  @override
  State<AIOnboardingScreen> createState() => _AIOnboardingScreenState();
}

class _AIOnboardingScreenState extends State<AIOnboardingScreen>
    with TickerProviderStateMixin {
  // Animation Controllers
  late AnimationController _backgroundController;
  late AnimationController _chatController;
  late AnimationController _typewriterController;
  late ConfettiController _confettiController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Chat State
  final List<ChatMessage> _messages = [];
  final ScrollController _scrollController = ScrollController();
  late TextEditingController _textController;

  // Onboarding State
  OnboardingStep _currentStep = OnboardingStep.welcome;
  bool _isAITyping = false;
  bool _showSoundtrackSelector = false;
  bool _isChatVisible = false;

  // User Data
  String? _userEmail;
  final List<String> _selectedGenres = [];
  final List<String> _selectedArtists = [];
  
  // Track selected artist metadata as they're selected
  final Map<String, String> _selectedArtistImageUrls = {};
  final Map<String, String> _selectedArtistSpotifyIds = {};
  
  // Track original artist names with proper capitalization (lowercase -> original)
  final Map<String, String> _originalArtistNames = {};
  List<Map<String, dynamic>> _availableArtists = [];
  List<Map<String, dynamic>> _availableSongs = [];
  Map<String, dynamic>? _selectedTrack;

  // New cycling loader state management
  ArtistSelectionState _artistSelectionState = const ArtistSelectionState(
    items: [],
    isLoading: false,
    hasMore: true,
    currentIndex: 0,
    batchCounts: {},
    selectedArtistKeys: {},
  );
  GenreCyclingArtistLoader? _artistLoader;

  SongSelectionState _songSelectionState = const SongSelectionState(
    items: [],
    isLoading: false,
    hasMore: true,
    currentIndex: 0,
    batchCounts: {},
    selectedTrack: null,
  );
  ArtistCyclingSongLoader? _songLoader;

  // Loading states
  bool _isLoadingArtists = false;
  bool _isLoadingSongs = false;
  bool _isProcessingGenreSelection = false;

  // Authentication
  final AIOnboardingAuthService _authService = AIOnboardingAuthService();
  bool _isAuthenticating = false;

  // Add this as a class-level variable
  List<String> _filteredGenres = [];
  late TextEditingController _artistSearchController;
  List<Map<String, dynamic>> _filteredArtists = [];
  late TextEditingController _songSearchController;

  // Search functionality
  Timer? _searchDebounceTimer;
  bool _isSearchingArtists = false;
  Timer? _songSearchDebounceTimer; // Add song search debounce timer
  bool _isSearchingSongs = false; // Add song search loading state
  List<Map<String, dynamic>> _filteredSongs = []; // Add filtered songs list

  // Similar artists state
  final Map<String, List<Map<String, dynamic>>> _similarArtistsMap = {};
  final Map<String, bool> _loadingSimilarArtists = {};
  final Map<String, Set<String>> _loadingSimilarArtistImages =
      {}; // Track which similar artists are loading images

  // Pagination state
  int _artistsOffset = 0;
  bool _hasMoreArtists = true;
  bool _isLoadingMoreArtists = false;
  int _songsOffset = 0;
  bool _hasMoreSongs = true;
  bool _isLoadingMoreSongs = false;
  late ScrollController _artistScrollController;
  late ScrollController _songScrollController;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();

    // Initialize the text controllers
    _textController = TextEditingController();
    _artistSearchController = TextEditingController();
    _songSearchController = TextEditingController();

    // Initialize scroll controllers
    _artistScrollController = ScrollController();
    _songScrollController = ScrollController();

    // Add scroll listeners for pagination
    _artistScrollController.addListener(_onArtistScroll);
    _songScrollController.addListener(_onSongScroll);

    // Check if user is already authenticated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthentication();
    });
  }

  void _checkAuthentication() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.loadStoredCredentials();

    final isAuthenticated = authProvider.isAuthenticated;
    print('🔑 [Onboarding] Authentication check: $isAuthenticated');

    if (isAuthenticated) {
      // Navigate to map screen if already authenticated
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/map');
      }
    } else {
      // Start onboarding if not authenticated
      _startOnboarding();
    }
  }

  void _initializeAnimations() {
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();

    _chatController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _typewriterController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _confettiController = ConfettiController(
      duration: const Duration(seconds: 3),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _chatController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _chatController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startOnboarding() {
    // Do nothing - wait for user to press "Get Started"
    // Removed automatic chat start
  }

  void _showChat() {
    setState(() {
      _isChatVisible = true;
    });

    // Reset and configure animation controllers for a more dramatic effect
    _chatController.reset(); // Ensure it starts from the beginning
    _chatController.duration =
        const Duration(milliseconds: 600); // Slightly longer duration

    // Create a more pronounced animation curve
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _chatController,
      curve: Curves.easeOutQuart, // More dramatic easing
    ));

    // Create a more pronounced slide animation
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.2), // Starts slightly lower
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _chatController,
      curve: Curves.easeOutQuart, // Matching the fade animation curve
    ));

    // Forward the animation
    _chatController.forward();

    // Add AI message after animation starts
    Timer(const Duration(milliseconds: 400), () {
      _addAIMessageWithProgression(
          AIDialogueContent.getRandomMessage(AIDialogueContent.greetings));
    });
  }

  void _addAIMessage(String message) {
    setState(() {
      _isAITyping = true;
    });

    _scrollToBottom();

    Timer(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _messages.add(ChatMessage(
            message: message,
            isAI: true,
            timestamp: DateTime.now(),
          ));
          _isAITyping = false;
        });
        _scrollToBottom();
      }
    });
  }

  void _addAIMessageWithProgression(String message) {
    print(
        '🎵 [Onboarding] Adding AI message with progression - Current step: $_currentStep');
    setState(() {
      _isAITyping = true;
    });

    _scrollToBottom();

    Timer(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _messages.add(ChatMessage(
            message: message,
            isAI: true,
            timestamp: DateTime.now(),
          ));
          _isAITyping = false;
        });
        _scrollToBottom();
        _handleStepProgression();
      }
    });
  }

  void _addUserMessage(String message) {
    setState(() {
      _messages.add(ChatMessage(
        message: message,
        isAI: false,
        timestamp: DateTime.now(),
      ));
    });
    _scrollToBottom();
  }

  void _scrollToBottom() {
    Timer(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _handleStepProgression() {
    final oldStep = _currentStep;
    switch (_currentStep) {
      case OnboardingStep.welcome:
        _currentStep = OnboardingStep.auth;
        break;
      case OnboardingStep.auth:
        _currentStep = OnboardingStep.genres;
        break;
      case OnboardingStep.genres:
        _currentStep = OnboardingStep.genreComment;
        break;
      case OnboardingStep.genreComment:
        _currentStep = OnboardingStep.artists;
        break;
      case OnboardingStep.artists:
        _currentStep = OnboardingStep.songSelection;
        break;
      case OnboardingStep.songSelection:
        _currentStep = OnboardingStep.pinPlacement;
        break;
      case OnboardingStep.pinPlacement:
        _currentStep = OnboardingStep.celebration;
        break;
      case OnboardingStep.celebration:
        _currentStep = OnboardingStep.completion;
        break;
      case OnboardingStep.completion:
        _navigateToMap();
        break;
    }
    print('🎵 [Onboarding] Step progression: $oldStep → $_currentStep');
  }

  void _navigateToMap() {
    // Save music preferences one final time before navigating
    _saveMusicPreferences();

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => const MapScreen()),
    );
  }

  @override
  void dispose() {
    // Save music preferences one final time before disposing
    if (_selectedGenres.isNotEmpty || _selectedArtists.isNotEmpty) {
      print('🎵 [Onboarding] Saving music preferences on dispose...');
      _saveMusicPreferences();
    }

    _backgroundController.dispose();
    _chatController.dispose();
    _typewriterController.dispose();
    _confettiController.dispose();
    _scrollController.dispose();
    _textController.dispose();
    _artistSearchController.dispose(); // Dispose artist search controller
    _songSearchController.dispose(); // Dispose song search controller
    _artistScrollController.dispose(); // Dispose artist scroll controller
    _songScrollController.dispose(); // Dispose song scroll controller

    // Cancel search debounce timer
    _searchDebounceTimer?.cancel();
    _songSearchDebounceTimer?.cancel(); // Cancel song search debounce timer

    // Stop background music when leaving onboarding
    // _stopBackgroundMusic(); // Removed

    super.dispose();
  }

  // Scroll listener for artist list pagination using infinite scroll controller
  void _onArtistScroll() {
    if (_artistScrollController.hasClients &&
        !_artistSelectionState.isLoading &&
        _artistSelectionState.hasMore) {
      final scrollPosition = _artistScrollController.position.pixels;
      final maxScrollExtent = _artistScrollController.position.maxScrollExtent;

      // Load more when scrolled 80% of the way
      if (scrollPosition >= maxScrollExtent * 0.8) {
        _loadMoreArtists();
      }
    }
  }

  // Scroll listener for song list pagination using cycling approach
  void _onSongScroll() {
    if (_songScrollController.hasClients &&
        !_songSelectionState.isLoading &&
        _songSelectionState.hasMore) {
      final scrollPosition = _songScrollController.position.pixels;
      final maxScrollExtent = _songScrollController.position.maxScrollExtent;

      // Load more when scrolled 80% of the way
      if (scrollPosition >= maxScrollExtent * 0.8) {
        _loadMoreSongs();
      }
    }
  }

  // Load more artists using cycling approach
  Future<void> _loadMoreArtists() async {
    if (_artistSelectionState.isLoading ||
        !_artistSelectionState.hasMore ||
        _artistLoader == null) {
      return;
    }

    setState(() {
      _artistSelectionState = _artistSelectionState.loading();
      _isLoadingMoreArtists = true;
    });

    try {
      // Load next batch using cycling loader
      final newState =
          await _artistLoader!.loadNextBatch(_artistSelectionState);

      setState(() {
        _artistSelectionState = newState;
        _isLoadingMoreArtists = false;

        // Update legacy variables for compatibility
        _availableArtists =
            newState.items.map((artist) => artist.toMap()).toList();
        _hasMoreArtists = newState.hasMore;
      });

      print(
          '🎵 [Onboarding] Loaded more artists: ${newState.items.length} total');
    } catch (e) {
      print('❌ [Onboarding] Error loading more artists: $e');
      setState(() {
        _artistSelectionState = _artistSelectionState
            .withError('Failed to load more artists: ${e.toString()}');
        _isLoadingMoreArtists = false;
      });
    }
  }

  // Load more songs using cycling approach
  Future<void> _loadMoreSongs() async {
    if (_songSelectionState.isLoading ||
        !_songSelectionState.hasMore ||
        _songLoader == null) {
      return;
    }

    setState(() {
      _songSelectionState = _songSelectionState.loading();
      _isLoadingMoreSongs = true;
    });

    try {
      // Load next batch using cycling loader
      final newState = await _songLoader!.loadNextBatch(_songSelectionState);

      setState(() {
        _songSelectionState = newState;
        _isLoadingMoreSongs = false;

        // Update legacy variables for compatibility
        _availableSongs = newState.items
            .map((song) => {
                  'id': song.id,
                  'title': song.title,
                  'artist': song.artist,
                  'album': song.album,
                  'album_cover': song.albumArtUrl ?? song.albumArt,
                  'uri': song.uri,
                  'url': song.url,
                  'duration_ms': song.durationMs,
                  'preview_url': song.previewUrl,
                  'popularity': song.popularity ?? 50,
                })
            .toList();
        _hasMoreSongs = newState.hasMore;
      });

      print(
          '🎵 [Onboarding] Loaded more songs: ${newState.items.length} total');
    } catch (e) {
      print('❌ [Onboarding] Error loading more songs: $e');
      setState(() {
        _songSelectionState = _songSelectionState
            .withError('Failed to load more songs: ${e.toString()}');
        _isLoadingMoreSongs = false;
      });
    }
  }

  // Enhanced loading card widget for progressive loading
  Widget _buildLoadingCard() {
    return const EnhancedLoadingCard(
      width: 150,
      height: 180,
      loadingText: 'Loading...',
      showShimmer: true,
    );
  }

  Widget _buildCompactLoadingCard() {
    return Container(
      width: 110,
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Theme.of(context).cardColor.withOpacity(0.1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Natural sizing
        children: [
          // Image placeholder area
          Container(
            margin: const EdgeInsets.all(4),
            height: 70,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).colorScheme.surfaceVariant,
            ),
            child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ),
          
          // Text area
          Padding(
            padding: const EdgeInsets.fromLTRB(6, 0, 6, 6),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Loading...',
                  style: TextStyle(
                    fontSize: 12,
                    height: 1.2,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Container(
                  height: 14,
                  width: 50,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor:
          isDarkMode ? AppColors.backgroundDark : AppColors.background,
      body: Stack(
        children: [
          // Animated Background
          _buildAnimatedBackground(isDarkMode),

          // Main Content
          SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(),

                // Chat Interface
                Expanded(
                  child: _isChatVisible
                      ? _buildChatInterface()
                      : _buildWelcomeScreen(),
                ),
              ],
            ),
          ),

          // Soundtrack Selector Overlay

          // Confetti
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirection: math.pi / 2,
              maxBlastForce: 5,
              minBlastForce: 2,
              emissionFrequency: 0.05,
              numberOfParticles: 50,
              gravity: 0.05,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBackground(bool isDarkMode) {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDarkMode
                  ? [
                      AppColors.backgroundDark,
                      AppColors.backgroundDark.withOpacity(0.8),
                      AppColors.primary.withOpacity(0.1),
                    ]
                  : [
                      AppColors.background,
                      AppColors.background.withOpacity(0.8),
                      AppColors.primary.withOpacity(0.05),
                    ],
              stops: [
                0.0,
                0.5 + 0.2 * math.sin(_backgroundController.value * 2 * math.pi),
                1.0,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // BOP Logo/Text
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(
                    'assets/images/logo/BOPmaps.png',
                    height: 32,
                    width: 32,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // const Text(
              //   'BOPMaps',
              //   style: TextStyle(
              //     fontSize: 24,
              //     fontWeight: FontWeight.bold,
              //     color: AppColors.primary,
              //   ),
              // ),
            ],
          ),
          const Spacer(),

          // Progress indicator
          _buildProgressIndicator(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final progress = _getProgressValue();
    return Container(
      width: 60,
      height: 6,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(3),
        color: Colors.grey.withOpacity(0.3),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progress,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            gradient: LinearGradient(
              colors: [
                AppColors.primary,
                AppColors.primary.withOpacity(0.7),
              ],
            ),
          ),
        ),
      ),
    );
  }

  double _getProgressValue() {
    switch (_currentStep) {
      case OnboardingStep.welcome:
        return 0.1;
      case OnboardingStep.auth:
        return 0.2;
      case OnboardingStep.genres:
        return 0.4;
      case OnboardingStep.genreComment:
        return 0.5;
      case OnboardingStep.artists:
        return 0.6;
      case OnboardingStep.songSelection:
        return 0.8;
      case OnboardingStep.pinPlacement:
        return 0.9;
      case OnboardingStep.celebration:
      case OnboardingStep.completion:
        return 1.0;
    }
  }

  Widget _buildWelcomeScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Lottie AI Icon
          Lottie.asset(
            'assets/anim/ai_search.json',
            width: 200,
            height: 200,
            fit: BoxFit.contain,
          ),

          const SizedBox(height: 20),

          // Welcome Text
          Text(
            'Welcome to BOPMaps',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.headlineLarge?.color,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 20),

          Text(
            'Discover, map, and share your musical journey',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.color
                  ?.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 40),

          // Get Started Button
          ElevatedButton(
            onPressed: () {
              setState(() {
                _showSoundtrackSelector = false;
                _showChat();
              });
            },
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(200, 50),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Get Started',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatInterface() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          margin: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Chat Messages
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: _messages.length + (_isAITyping ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _messages.length && _isAITyping) {
                      return _buildTypingIndicator();
                    }

                    final message = _messages[index];
                    return _buildMessageBubble(message);
                  },
                ),
              ),

              // Current Step Content
              _buildStepContent(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (message.isAI) ...[
            // AI Avatar
            Container(
              width: 40,
              height: 40,
              margin: const EdgeInsets.only(right: 12),
              child: Lottie.asset(
                'assets/anim/ai_search.json',
                fit: BoxFit.cover,
              ),
            ),

            // AI Message or Special Content
            Expanded(
              child: message.specialContent != null
                  ? message.specialContent! // Show special content if available
                  : Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(4),
                          topRight: Radius.circular(16),
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                      ),
                      child: Text(
                        message.message,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 16,
                        ),
                      ),
                    ),
            ),
          ] else ...[
            // User Message
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      constraints: BoxConstraints(
                        maxWidth: MediaQuery.of(context).size.width * 0.7,
                      ),
                      decoration: const BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(4),
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                      ),
                      child: Text(
                        message.message,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        softWrap: true,
                        overflow: TextOverflow.visible,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            margin: const EdgeInsets.only(right: 12),
            child: Lottie.asset(
              'assets/anim/ai_search.json',
              fit: BoxFit.cover,
            ),
          ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(16),
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: _buildTypingAnimation(),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingAnimation() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _backgroundController,
          builder: (context, child) {
            final offset = math.sin(
                (_backgroundController.value * 4 * math.pi) + (index * 0.5));
            return Transform.translate(
              offset: Offset(0, offset * 3),
              child: Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.primary.withOpacity(0.6),
                ),
              ),
            );
          },
        );
      }),
    );
  }

  Widget _buildStepContent() {
    switch (_currentStep) {
      case OnboardingStep.auth:
        return _buildAuthOptions();
      case OnboardingStep.genres:
        return _buildGenreSelector();
      case OnboardingStep.genreComment:
        // Show loading or transition to artist selection
        return _isLoadingArtists
            ? _buildModernArtistLoadingState()
            : _buildArtistSelector();
      case OnboardingStep.artists:
        return _buildArtistSelector();
      case OnboardingStep.songSelection:
        return _buildSongSelector();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildAuthOptions() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Terms and Privacy Notice
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                ),
                children: [
                  const TextSpan(text: 'By using BOPMaps, you accept our '),
                  WidgetSpan(
                    child: GestureDetector(
                      onTap: () => _openTermsOfService(),
                      child: Text(
                        'Terms of Service',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.primary,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ),
                  const TextSpan(text: ' and '),
                  WidgetSpan(
                    child: GestureDetector(
                      onTap: () => _openPrivacyPolicy(),
                      child: Text(
                        'Privacy Policy',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.primary,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Apple Sign In
          ElevatedButton.icon(
            onPressed: _isAuthenticating ? null : () => _handleAppleMusicSignIn(),
            icon: _isAuthenticating
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(FontAwesomeIcons.apple),
            label: Text(
                _isAuthenticating ? 'Signing in...' : 'Continue with Apple'),
            style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 48),
                backgroundColor: Colors.black,
                foregroundColor: Colors.white,
                iconAlignment: IconAlignment.start),
          ),

          const SizedBox(height: 12),

          // Google Sign In
          ElevatedButton.icon(
            onPressed: _isAuthenticating ? null : () => _handleGoogleSignIn(),
            icon: _isAuthenticating
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(FontAwesomeIcons.google),
            label: Text(
                _isAuthenticating ? 'Signing in...' : 'Continue with Google'),
            style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 48),
                backgroundColor: Colors.white,
                foregroundColor: Colors.black87,
                iconAlignment: IconAlignment.start),
          ),

          const SizedBox(height: 12),

          // Email Sign In
          ElevatedButton.icon(
            onPressed: _isAuthenticating ? null : () => _handleEmailSignIn(),
            icon: _isAuthenticating
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.email),
            label: Text(
                _isAuthenticating ? 'Signing in...' : 'Continue with Email'),
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 48),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenreSelector() {
    // Use the canonical genres from SpotifyGenreService
    final popularGenres = SpotifyGenreService.getMainGenresForSelection();

    // Use filtered genres if search is active, otherwise use all genres
    final displayGenres =
        _filteredGenres.isNotEmpty ? _filteredGenres : popularGenres;

    // Get keyboard and screen info for responsive design
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final screenHeight = MediaQuery.of(context).size.height;
    final isKeyboardVisible = keyboardHeight > 0;
    final hasSelectedGenres = _selectedGenres.isNotEmpty;
    
    // Calculate available space more intelligently
    final availableHeight = screenHeight - keyboardHeight;
    final headerHeight = isKeyboardVisible ? 50 : 90; // Header + spacing
    final searchBarHeight = 68; // Search bar + spacing
    final buttonHeight = hasSelectedGenres ? (isKeyboardVisible ? 68 : 84) : 0; // Button + spacing
    final padding = isKeyboardVisible ? 48 : 64; // Top/bottom padding
    final safeArea = MediaQuery.of(context).padding.top + MediaQuery.of(context).padding.bottom;
    
    final genreChipsHeight = (availableHeight - headerHeight - searchBarHeight - buttonHeight - padding - safeArea - 100).clamp(120.0, 300.0);
    
    return Container(
      padding: EdgeInsets.fromLTRB(
        12, // Reduced from 20 to 12 for more space
        16, 
        12, // Reduced from 20 to 12 for more space
        isKeyboardVisible ? 8 : 16
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Compact header when keyboard is visible
          if (!isKeyboardVisible) ...[
            // Full Header with Counter
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.music_note_rounded,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Choose Your Favorite Genres',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).textTheme.headlineSmall?.color,
                      letterSpacing: -0.3,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primary.withOpacity(0.15),
                        AppColors.primary.withOpacity(0.08),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: AppColors.primary.withOpacity(0.3),
                      width: 1.5,
                    ),
                  ),
                  child: Text(
                    '${_selectedGenres.length}/20',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w700,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ] else ...[
            // Compact header for keyboard mode
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Select Genres',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.headlineSmall?.color,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_selectedGenres.length}/20',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],
          
          // Modern Search Bar
          Container(
            height: 48,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(24), // Perfect pill shape
              border: Border.all(
                color: _textController.text.isNotEmpty 
                    ? AppColors.primary.withOpacity(0.5)
                    : Theme.of(context).colorScheme.outline.withOpacity(0.2),
                width: _textController.text.isNotEmpty ? 2.0 : 1.0,
              ),
              boxShadow: _textController.text.isNotEmpty ? [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.15),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ] : null,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(24), // Ensure content is clipped to rounded shape
              child: Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: Icon(
                      Icons.search_rounded,
                      size: 20,
                      color: _textController.text.isNotEmpty
                          ? AppColors.primary
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Expanded(
                    child: TextField(
                      controller: _textController,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      cursorColor: AppColors.primary,
                      decoration: InputDecoration(
                        hintText: 'Search genres...',
                        hintStyle: TextStyle(
                          color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.6),
                          fontSize: 15,
                          fontWeight: FontWeight.w400,
                        ),
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        focusedErrorBorder: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 14,
                        ),
                        isDense: true,
                      ),
                      onChanged: (value) {
                        setState(() {
                          if (value.isEmpty) {
                            _filteredGenres.clear();
                          } else {
                            _filteredGenres = popularGenres
                                .where((genre) =>
                                    genre.toLowerCase().contains(value.toLowerCase()))
                                .toList();
                          }
                        });
                      },
                    ),
                  ),
                  if (_textController.text.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: IconButton(
                        icon: Icon(
                          Icons.close_rounded,
                          size: 18,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          setState(() {
                            _filteredGenres.clear();
                            _textController.clear();
                          });
                        },
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                        padding: EdgeInsets.zero,
                        splashRadius: 16,
                      ),
                    ),
                ],
              ),
            ),
          ),

          SizedBox(height: isKeyboardVisible ? 12 : 20),

          // Genre Chips with dynamically calculated height
          Container(
            height: genreChipsHeight,
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 350),
              switchInCurve: Curves.easeOutCubic,
              switchOutCurve: Curves.easeInCubic,
              child: (_textController.text.isNotEmpty && _filteredGenres.isEmpty)
                  ? _buildNoGenresFound()
                  : _buildGenreChipsGrid(displayGenres, isKeyboardVisible),
            ),
          ),

          // Modern Done Button - only show when genres are selected
          if (hasSelectedGenres) ...[
            SizedBox(height: isKeyboardVisible ? 6 : 12),
            Container(
              width: double.infinity,
              height: isKeyboardVisible ? 48 : 52,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(isKeyboardVisible ? 12 : 16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: isKeyboardVisible ? 8 : 12,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: _isProcessingGenreSelection
                    ? null
                    : () => _handleGenreSelection(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(isKeyboardVisible ? 12 : 16),
                  ),
                ),
                child: _isProcessingGenreSelection
                    ? SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.check_rounded, size: 18),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              isKeyboardVisible 
                                  ? 'Continue (${_selectedGenres.length})' 
                                  : 'Continue with ${_selectedGenres.length} Genre${_selectedGenres.length == 1 ? '' : 's'}',
                              style: TextStyle(
                                fontSize: isKeyboardVisible ? 14 : 16,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNoGenresFound() {
    return Center(
      key: const ValueKey('no_genres'),
      child: SingleChildScrollView(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxHeight: 120),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.search_off_rounded,
                  size: 28,
                  color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.5),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'No genres found',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 6),
              Text(
                'Try a different search term',
                style: TextStyle(
                  fontSize: 13,
                  color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGenreChipsGrid(List<String> displayGenres, bool isKeyboardVisible) {
    return SingleChildScrollView(
      key: const ValueKey('genre_chips'),
      padding: EdgeInsets.only(
        bottom: isKeyboardVisible ? 8 : 16,
      ),
      child: Wrap(
        spacing: 8,
        runSpacing: 10,
        children: displayGenres
            .map((genre) => _buildGenreChip(genre))
            .toList(),
      ),
    );
  }

  Widget _buildGenreChip(String genre) {
    final isSelected = _selectedGenres.contains(genre);
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: FilterChip(
        label: Text(
          genre,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isSelected 
                ? Colors.white 
                : Theme.of(context).colorScheme.onSurface,
          ),
        ),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            if (selected) {
              _selectedGenres.add(genre);
            } else {
              _selectedGenres.remove(genre);
            }
          });
        },
        backgroundColor: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        selectedColor: AppColors.primary,
        checkmarkColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(
            color: isSelected 
                ? AppColors.primary 
                : Theme.of(context).colorScheme.outline.withOpacity(0.2),
            width: 1.5,
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildArtistSelector() {
    print(
        '🎵 [Onboarding] Building artist selector with ${_artistSelectionState.items.length} artists');

    // Use filtered artists if search is active, otherwise use cycling loader results
    final displayArtists = _filteredArtists.isNotEmpty
        ? _filteredArtists
            .map((artist) => ArtistWithGenre.fromMap(artist))
            .toList()
        : _artistSelectionState.items;
    final isSearchActive = _artistSearchController.text.isNotEmpty &&
        _artistSearchController.text.length >= 3;

    // Get keyboard and screen info for responsive design
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final screenHeight = MediaQuery.of(context).size.height;
    final isKeyboardVisible = keyboardHeight > 0;
    final hasSelectedArtists = _selectedArtists.isNotEmpty;
    
    // Calculate available space more intelligently
    final availableHeight = screenHeight - keyboardHeight;
    final headerHeight = isKeyboardVisible ? 50 : 90; // Header + spacing
    final searchBarHeight = 68; // Search bar + spacing
    final descriptionHeight = 40; // Description text + spacing
    final buttonHeight = hasSelectedArtists ? (isKeyboardVisible ? 68 : 84) : 0; // Button + spacing
    final padding = isKeyboardVisible ? 48 : 64; // Top/bottom padding
    final safeArea = MediaQuery.of(context).padding.top + MediaQuery.of(context).padding.bottom;
    
    // Make it much taller when no keyboard - use most of the screen!
    final artistListHeight = isKeyboardVisible 
        ? (availableHeight - headerHeight - searchBarHeight - descriptionHeight - buttonHeight - padding - safeArea - 50).clamp(140.0, 250.0)
        : (availableHeight - headerHeight - searchBarHeight - descriptionHeight - buttonHeight - padding - safeArea - 50).clamp(200.0, 500.0);

    return Container(
      padding: EdgeInsets.fromLTRB(
        12, // Reduced from 20 to 12 for more space
        16, 
        12, // Reduced from 20 to 12 for more space
        isKeyboardVisible ? 8 : 16
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Compact header when keyboard is visible
          if (!isKeyboardVisible) ...[
            // Full Header with Counter
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.person_rounded,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Choose Your Favorite Artists',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).textTheme.headlineSmall?.color,
                      letterSpacing: -0.3,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primary.withOpacity(0.15),
                        AppColors.primary.withOpacity(0.08),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: AppColors.primary.withOpacity(0.3),
                      width: 1.5,
                    ),
                  ),
                  child: Text(
                    '${_selectedArtists.length}/10',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w700,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ] else ...[
            // Compact header for keyboard mode
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Select Artists',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.headlineSmall?.color,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_selectedArtists.length}/10',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],
          
          // Modern Search Bar
          Container(
            height: 48,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: _artistSearchController.text.isNotEmpty 
                    ? AppColors.primary.withOpacity(0.5)
                    : Theme.of(context).colorScheme.outline.withOpacity(0.2),
                width: _artistSearchController.text.isNotEmpty ? 2.0 : 1.0,
              ),
              boxShadow: _artistSearchController.text.isNotEmpty ? [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.15),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ] : null,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: _isSearchingArtists
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                            ),
                          )
                        : Icon(
                            Icons.search_rounded,
                            size: 20,
                            color: _artistSearchController.text.isNotEmpty
                                ? AppColors.primary
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                  ),
                  Expanded(
                    child: TextField(
                      controller: _artistSearchController,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      cursorColor: AppColors.primary,
                      decoration: InputDecoration(
                        hintText: 'Search artists...',
                        hintStyle: TextStyle(
                          color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.6),
                          fontSize: 15,
                          fontWeight: FontWeight.w400,
                        ),
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        focusedErrorBorder: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 14,
                        ),
                        isDense: true,
                      ),
                      onChanged: (value) {
                        _performArtistSearch(value);
                      },
                    ),
                  ),
                  if (_artistSearchController.text.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: IconButton(
                        icon: Icon(
                          Icons.close_rounded,
                          size: 18,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          setState(() {
                            _filteredArtists.clear();
                            _artistSearchController.clear();
                            _searchDebounceTimer?.cancel();
                            // Re-enable pagination when clearing search
                            _hasMoreArtists = _availableArtists.isNotEmpty;
                            _isLoadingMoreArtists = false;
                          });
                        },
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                        padding: EdgeInsets.zero,
                        splashRadius: 16,
                      ),
                    ),
                ],
              ),
            ),
          ),

          SizedBox(height: isKeyboardVisible ? 8 : 12),

          // Description
          Text(
            isSearchActive
                ? 'Search results from Spotify:'
                : 'Artists from the genres you love:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),

          SizedBox(height: isKeyboardVisible ? 12 : 20),

          // Artist List with proper height constraint
          SizedBox(
            height: artistListHeight.clamp(100.0, 200.0), // Reasonable height range
            child: _buildArtistList(displayArtists.map((artist) => {
              'name': artist.name,
              'image_url': artist.imageUrl,
              'genres': artist.genres,
              'is_similar': artist.isSimilar,
              'source_genre': artist.sourceGenre,
              'is_search_result': false,
            }).toList()),
          ),

          // Continue Button - Modern styling
          if (hasSelectedArtists) ...[
            SizedBox(height: isKeyboardVisible ? 8 : 16),
            Container(
              width: double.infinity,
              height: isKeyboardVisible ? 48 : 52,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(isKeyboardVisible ? 12 : 16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: isKeyboardVisible ? 8 : 12,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: () => _handleArtistSelection(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(isKeyboardVisible ? 12 : 16),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.check_rounded, size: 18),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Text(
                        isKeyboardVisible 
                            ? 'Continue (${_selectedArtists.length})' 
                            : 'Continue with ${_selectedArtists.length} Artist${_selectedArtists.length == 1 ? '' : 's'}',
                        style: TextStyle(
                          fontSize: isKeyboardVisible ? 14 : 16,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Build a combined list of artists and their similar artists
  List<Map<String, dynamic>> _buildCombinedArtistList(
      List<Map<String, dynamic>> artists) {
    final combinedList = <Map<String, dynamic>>[];

    for (final artist in artists) {
      // Add the main artist
      combinedList.add(artist);

      // Add similar artists if this artist is selected
      if (_selectedArtists.contains(artist['name'])) {
        final similarArtists = _similarArtistsMap[artist['name']] ?? [];
        // Ensure similar artists have the correct source_genre from their parent
        final similarArtistsWithGenre = similarArtists.map((similarArtist) {
          final updatedSimilarArtist = Map<String, dynamic>.from(similarArtist);
          updatedSimilarArtist['source_genre'] = artist['source_genre'] ?? 'unknown';
          return updatedSimilarArtist;
        }).toList();
        combinedList.addAll(similarArtistsWithGenre);
      }
    }

    return combinedList;
  }



  // Modify the existing _buildArtistList method to accept a list parameter
  Widget _buildArtistList(List<Map<String, dynamic>> artists) {
    print(
        '🎵 [Onboarding] Building artist list - Loading: $_isLoadingArtists, Artists: ${artists.length}');
    if (_isLoadingArtists) {
      return Container(
        height: 180,
        alignment: Alignment.center,
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Finding artists for your genres...'),
          ],
        ),
      );
    }

    if (artists.isEmpty) {
      final isSearchActive = _artistSearchController.text.isNotEmpty &&
          _artistSearchController.text.length >= 3;

      return Container(
        height: 180,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isSearchActive ? Icons.search_off : Icons.search_off,
              size: 48,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              isSearchActive ? 'No artists found' : 'No artists found',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isSearchActive
                  ? 'Try a different search term or check your connection.'
                  : 'Try selecting different genres or check your connection.',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final combinedArtists = _buildCombinedArtistList(artists);

    return SizedBox(
      height: 180, // Fixed height for horizontal list
      child: ListView.builder(
        controller: _artistScrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        scrollDirection: Axis.horizontal,
        itemCount: combinedArtists.length + (_hasMoreArtists ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading card at the end if we're loading more or there are more results
          if (index == combinedArtists.length) {
            if (_isLoadingMoreArtists || _hasMoreArtists) {
              return _buildLoadingCard();
            }
            return const SizedBox.shrink();
          }
          final artist = combinedArtists[index];
          final isSelected = _selectedArtists.contains(artist['name']);
          final isSimilar = artist['is_similar'] == true;
          final isLoadingSimilar =
              _loadingSimilarArtists[artist['name']] == true;

          // Check if this similar artist is still loading its image
          bool isLoadingImage = false;
          if (isSimilar) {
            // Find which main artist this similar artist belongs to
            for (final mainArtist in _selectedArtists) {
              final loadingSet = _loadingSimilarArtistImages[mainArtist];
              if (loadingSet != null && loadingSet.contains(artist['name'])) {
                isLoadingImage = true;
                print(
                    '🎵 [Onboarding] Artist ${artist['name']} is still loading image for main artist $mainArtist');
                break;
              }
            }
          }

          return Container(
            width: 150, // Fixed width for each card
            margin: const EdgeInsets.only(right: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? AppColors.primary : Colors.transparent,
                width: 2,
              ),
              color: Theme.of(context).cardColor.withOpacity(0.1),
            ),
            child: InkWell(
              onTap: () => _toggleArtistSelection(
                  artist['name'], artist['source_genre'] ?? 'unknown'),
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Artist Image
                  if (artist['image_url'] != null && !isLoadingImage)
                    Positioned.fill(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          artist['image_url'],
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            color: Theme.of(context)
                                .colorScheme
                                .surfaceContainerHighest,
                            child: Icon(
                              Icons.person,
                              size: 40,
                              color: Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.5),
                            ),
                          ),
                        ),
                      ),
                    ),
                  // Show loading placeholder for similar artists without images
                  if (isSimilar &&
                      (artist['image_url'] == null || isLoadingImage))
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Loading...',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                  // Gradient overlay
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.7),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Artist Info
                  Positioned(
                    bottom: 12,
                    left: 12,
                    right: 12,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          artist['name'],
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (artist['genres']?.isNotEmpty == true) ...[
                          const SizedBox(height: 4),
                          Text(
                            (artist['genres'] as List).take(2).join(', '),
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                        if (isSimilar) ...[
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'Similar',
                              style: TextStyle(
                                color: Colors.blue,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                        if (artist['is_search_result'] == true) ...[
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'Search',
                              style: TextStyle(
                                color: Colors.green,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Selection indicator
                  if (isSelected)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: AppColors.primary,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),

                  // Loading indicator for similar artists (when loading the list)
                  if (isLoadingSimilar)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                      ),
                    ),
                  // Loading indicator for similar artist images
                  if (isSimilar && isLoadingImage && !isLoadingSimilar)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _toggleArtistSelection(String artistName, String sourceGenre) {
    print(
        '🎵 [Onboarding] _toggleArtistSelection called with: "$artistName", "$sourceGenre"');
    final artistKey =
        SelectionKeyManager.generateArtistKey(artistName, sourceGenre);
    print('🎵 [Onboarding] Generated artist key: "$artistKey"');
    final newSelectedKeys =
        Set<String>.from(_artistSelectionState.selectedArtistKeys);
    print('🎵 [Onboarding] Current selected keys: $newSelectedKeys');

    setState(() {
      if (newSelectedKeys.contains(artistKey)) {
        newSelectedKeys.remove(artistKey);
        
        // Remove artist metadata from tracking maps when deselecting
        _selectedArtistImageUrls.remove(artistName);
        _selectedArtistSpotifyIds.remove(artistName);
        _originalArtistNames.remove(artistName.toLowerCase());
        print('🗑️ [Onboarding] Removed metadata for deselected artist: $artistName');
        
        // Remove similar artists when deselecting
        final newSimilarArtistsMap = Map<String, List<ArtistWithGenre>>.from(
            _artistSelectionState.similarArtistsMap);
        newSimilarArtistsMap.remove(artistName);
        _similarArtistsMap.remove(artistName);
        _loadingSimilarArtistImages.remove(artistName);

        _artistSelectionState = _artistSelectionState
            .withSelection(newSelectedKeys)
            .withSimilarArtists(newSimilarArtistsMap);

        print(
            '🎵 [Onboarding] Deselected artist: $artistName from $sourceGenre');
      } else {
        // Enforce artist selection limit of 50
        if (_selectedArtists.length >= 50) {
          print('🎵 [Onboarding] Artist selection limit reached (50/50)');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Maximum 50 artists can be selected'),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
          return;
        }

        // Check for duplicate artist selection (handles variations and Spotify ID matches)
        final spotifyId = _getSpotifyIdForArtist(artistName);
        if (_isArtistAlreadySelected(artistName, spotifyId: spotifyId)) {
          print('🎵 [Onboarding] Duplicate artist detected: $artistName');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$artistName is already selected'),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
          return;
        }

        newSelectedKeys.add(artistKey);
        _artistSelectionState =
            _artistSelectionState.withSelection(newSelectedKeys);

        // Store original artist name with proper capitalization
        _originalArtistNames[artistName.toLowerCase()] = artistName;
        print('✨ [Onboarding] Stored original name: "$artistName" for key: "${artistName.toLowerCase()}"');
        
        // Capture artist metadata when selecting
        _captureArtistMetadata(artistName);

        print(
            '🎵 [Onboarding] Selected artist: $artistName from $sourceGenre, loading similar artists...');
        // Load similar artists when selecting
        _loadSimilarArtists(artistName);
      }

      // Update legacy _selectedArtists using original names with proper capitalization
      _selectedArtists.clear();
      final lowercaseNames = SelectionKeyManager.getSelectedArtistNames(newSelectedKeys);
      for (final lowercaseName in lowercaseNames) {
        final originalName = _originalArtistNames[lowercaseName] ?? lowercaseName;
        _selectedArtists.add(originalName);
      }

      print(
          '🎵 [Onboarding] Total selected artists: ${_selectedArtists.length}');
    });
  }

  /// Capture artist metadata (image URL and Spotify ID) when selecting an artist
  void _captureArtistMetadata(String artistName) {
    print('📸 [Onboarding] Capturing metadata for artist: "$artistName"');
    
    // Search in artist selection state items (primary source)
    for (final artist in _artistSelectionState.items) {
      if (artist.name == artistName) {
        if (artist.imageUrl != null && artist.imageUrl!.isNotEmpty) {
          _selectedArtistImageUrls[artistName] = artist.imageUrl!;
          print('✅ [Onboarding] Captured image URL from selection state: ${artist.imageUrl}');
        }
        
        if (artist.id.isNotEmpty && artist.id != artistName.toLowerCase().replaceAll(' ', '-')) {
          _selectedArtistSpotifyIds[artistName] = artist.id;
          print('✅ [Onboarding] Captured Spotify ID from selection state: ${artist.id}');
        }
        return; // Found the artist, no need to continue searching
      }
    }
    
    // Fallback: Search in available artists data (legacy compatibility)
    for (final artist in _availableArtists) {
      if (artist['name'] == artistName) {
        if (artist['image_url'] != null && artist['image_url'].isNotEmpty) {
          _selectedArtistImageUrls[artistName] = artist['image_url'];
          print('✅ [Onboarding] Captured image URL from available artists: ${artist['image_url']}');
        }
        
        if (artist['spotify_id'] != null && artist['spotify_id'].isNotEmpty) {
          _selectedArtistSpotifyIds[artistName] = artist['spotify_id'];
          print('✅ [Onboarding] Captured Spotify ID from available artists: ${artist['spotify_id']}');
        }
        return;
      }
    }
    
    // Also check similar artists maps in case this artist came from there
    for (final similarList in _artistSelectionState.similarArtistsMap.values) {
      for (final artist in similarList) {
        if (artist.name == artistName) {
          if (artist.imageUrl != null && artist.imageUrl!.isNotEmpty) {
            _selectedArtistImageUrls[artistName] = artist.imageUrl!;
            print('✅ [Onboarding] Captured image URL from similar artists: ${artist.imageUrl}');
          }
          
          if (artist.id.isNotEmpty && artist.id != artistName.toLowerCase().replaceAll(' ', '-')) {
            _selectedArtistSpotifyIds[artistName] = artist.id;
            print('✅ [Onboarding] Captured Spotify ID from similar artists: ${artist.id}');
          }
          return;
        }
      }
    }
    
    print('⚠️ [Onboarding] Could not find metadata for artist: "$artistName"');
  }

  /// Check if an artist name represents a collaboration
  bool _isCollaboration(String artistName) {
    // Common collaboration indicators
    final collaborationIndicators = [
      ' & ', ' and ', ' feat. ', ' feat ', ' ft. ', ' ft ', ' featuring ',
      ' vs. ', ' vs ', ' x ', ' X ', ' with ', ' + ', ' / ', ' | ',
      ' meets ', ' meets. ', ' vs ', ' versus ', ' collaboration ',
    ];

    final lowerName = artistName.toLowerCase();

    // Check for collaboration indicators
    for (final indicator in collaborationIndicators) {
      if (lowerName.contains(indicator.toLowerCase())) {
        print('🚫 [Onboarding] Skipping collaboration: "$artistName" (contains "$indicator")');
        return true;
      }
    }

    // Check for multiple artist names separated by commas
    if (artistName.contains(',') && artistName.split(',').length > 1) {
      print('🚫 [Onboarding] Skipping collaboration: "$artistName" (comma-separated)');
      return true;
    }

    return false;
  }

  /// Check if an artist is already selected (handles variations and duplicates)
  bool _isArtistAlreadySelected(String artistName, {String? spotifyId}) {
    // If no artists are selected yet, this can't be a duplicate
    if (_selectedArtists.isEmpty) {
      return false;
    }

    final nameLower = artistName.toLowerCase().trim();

    // Normalize artist name for comparison (remove common variations)
    final normalizedName = _normalizeArtistNameForComparison(nameLower);

    // Check against main selected artists by name
    for (final selectedArtist in _selectedArtists) {
      final selectedNormalized = _normalizeArtistNameForComparison(selectedArtist.toLowerCase().trim());
      if (normalizedName == selectedNormalized) {
        print('🚫 [Onboarding] Artist "$artistName" already selected as "$selectedArtist" (name match)');
        return true;
      }
    }

    // If we have a Spotify ID, check against existing Spotify IDs of SELECTED artists only
    if (spotifyId != null && spotifyId.isNotEmpty) {
      // Check if any selected artist has this Spotify ID
      for (final selectedArtist in _selectedArtists) {
        final selectedSpotifyId = _getSpotifyIdForArtist(selectedArtist);
        if (selectedSpotifyId != null && selectedSpotifyId.isNotEmpty && selectedSpotifyId == spotifyId) {
          print('🚫 [Onboarding] Artist "$artistName" has same Spotify ID as already selected "$selectedArtist": $spotifyId');
          return true;
        }
      }
    }

    return false;
  }

  /// Normalize artist name for comparison to handle variations
  String _normalizeArtistNameForComparison(String name) {
    return name
        .toLowerCase()
        .trim()
        // Remove common prefixes/suffixes
        .replaceAll(RegExp(r'^the\s+'), '')
        .replaceAll(RegExp(r'\s+the$'), '')
        // Remove special characters and extra spaces
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim()
        // Handle common name variations
        .replaceAll('young thug', 'youngthug')
        .replaceAll(r'a$ap', 'asap')
        .replaceAll('lil ', 'lil')
        .replaceAll('big ', 'big')
        .replaceAll('21 savage', '21savage')
        .replaceAll('50 cent', '50cent')
        // Remove dots and apostrophes
        .replaceAll('.', '')
        .replaceAll("'", '');
  }

  Future<void> _loadSimilarArtists(String artistName) async {
    if (_loadingSimilarArtists[artistName] == true) return;

    print('🎵 [Onboarding] Starting to load similar artists for: $artistName');

    setState(() {
      _loadingSimilarArtists[artistName] = true;
    });

    try {
      final lastfmService = LastFmService();
      final similarArtistNames =
          await lastfmService.getSimilarArtists(artistName, limit: 10); // Get more to filter duplicates

      print(
          '🎵 [Onboarding] Got ${similarArtistNames.length} similar artists from Last.fm: ${similarArtistNames.join(', ')}');

      // Filter out already selected artists and get unique similar artists, skipping collaborations
      final Set<String> allSelectedArtistNames = _selectedArtists.map((name) => name.toLowerCase()).toSet();

      // Also collect all artists that are already showing as similar artists for other selected artists
      final Set<String> allExistingSimilarArtists = <String>{};
      for (final similarList in _similarArtistsMap.values) {
        for (final artist in similarList) {
          allExistingSimilarArtists.add(artist['name'].toString().toLowerCase());
        }
      }

      // Also check the new state system for similar artists
      for (final similarList in _artistSelectionState.similarArtistsMap.values) {
        for (final artist in similarList) {
          allExistingSimilarArtists.add(artist.name.toLowerCase());
        }
      }

      final List<String> filteredSimilarArtists = [];

      for (final name in similarArtistNames) {
        final nameLower = name.toLowerCase();
        if (name.isNotEmpty &&
            !allSelectedArtistNames.contains(nameLower) && // Not in main selected artists
            !allExistingSimilarArtists.contains(nameLower) && // Not already a similar artist for someone else
            !filteredSimilarArtists.any((existing) => existing.toLowerCase() == nameLower) && // Not already in current batch
            !_isCollaboration(name) && // Skip collaborations
            !_isArtistAlreadySelected(name)) { // Use enhanced duplicate detection
          filteredSimilarArtists.add(name);
          print('✅ [Onboarding] Adding unique similar artist: "$name" for "$artistName"');
          // Stop when we have enough unique similar artists
          if (filteredSimilarArtists.length >= 3) break;
        } else {
          // Log why this artist was skipped
          if (allSelectedArtistNames.contains(nameLower)) {
            print('🚫 [Onboarding] Skipping "$name" - already selected as main artist');
          } else if (allExistingSimilarArtists.contains(nameLower)) {
            print('🚫 [Onboarding] Skipping "$name" - already showing as similar artist for another artist');
          } else if (_isCollaboration(name)) {
            // Already logged in _isCollaboration method
          } else if (_isArtistAlreadySelected(name)) {
            print('🚫 [Onboarding] Skipping "$name" - duplicate artist (name variation or same Spotify ID)');
          } else {
            print('🚫 [Onboarding] Skipping "$name" - duplicate in current batch');
          }
        }
      }

      print(
          '🎵 [Onboarding] Filtered to ${filteredSimilarArtists.length} unique similar artists: ${filteredSimilarArtists.join(', ')}');

      // Convert to ArtistWithGenre objects for the new state system
      final List<ArtistWithGenre> similarArtists = [];

      for (final name in filteredSimilarArtists) {
        similarArtists.add(ArtistWithGenre(
          id: name.toLowerCase().replaceAll(' ', '-'),
          name: name,
          imageUrl: null, // Will be filled by Spotify if available
          genres: [], // Will be filled by Spotify if available
          popularity: 50, // Default popularity
          url: '',
          sourceGenre: 'similar to $artistName',
          isSimilar: true,
        ));
      }

      // Update both the legacy map and the new state system
      final legacySimilarArtists = filteredSimilarArtists
          .map((name) => {
                'id': name.toLowerCase().replaceAll(' ', '-'),
                'name': name,
                'image_url': null,
                'genres': <String>[],
                'popularity': 50,
                'url': '',
                'is_similar': true,
              })
          .toList();

      setState(() {
        // Update legacy map for compatibility
        _similarArtistsMap[artistName] = legacySimilarArtists;
        _loadingSimilarArtists[artistName] = false;
        _loadingSimilarArtistImages[artistName] = filteredSimilarArtists.toSet();

        // Update the new state system
        final newSimilarArtistsMap = Map<String, List<ArtistWithGenre>>.from(
            _artistSelectionState.similarArtistsMap);
        newSimilarArtistsMap[artistName] = similarArtists;
        _artistSelectionState =
            _artistSelectionState.withSimilarArtists(newSimilarArtistsMap);
      });

      print(
          '🎵 [Onboarding] Added similar artists to both legacy and new state systems, now loading images from Spotify...');

      // Try to enhance with Spotify data for images using client credentials
      try {
        final spotifyService = SpotifyService();

        // Search for each artist individually to get proper Spotify artist data
        for (int i = 0; i < similarArtists.length; i++) {
          final artist = similarArtists[i];
          final legacyArtist = legacySimilarArtists[i];

          try {
            print(
                '🎵 [Onboarding] Searching for individual artist: ${artist.name}');

            // First try to get artist data directly
            final artistData = await spotifyService.getArtist(artist.name);

            if (artistData != null && artistData['id'] != null) {
              // Extract image URL from Spotify API response
              String? imageUrl;
              if (artistData['images'] != null && (artistData['images'] as List).isNotEmpty) {
                imageUrl = artistData['images'][0]['url'] as String?;
              }

              // Update the ArtistWithGenre object with proper artist data
              final updatedArtist = artist.copyWith(
                imageUrl: imageUrl,
                genres: (artistData['genres'] as List?)?.cast<String>() ?? [],
                popularity: artistData['popularity'] ?? 50,
                url: artistData['external_urls']?['spotify'] ?? '',
                id: artistData['id'], // This is now the proper Spotify artist ID
              );
              similarArtists[i] = updatedArtist;

              // Update legacy format
              legacyArtist['image_url'] = imageUrl;
              legacyArtist['genres'] = (artistData['genres'] as List?)?.cast<String>() ?? [];
              legacyArtist['popularity'] = artistData['popularity'] ?? 50;
              legacyArtist['url'] = artistData['external_urls']?['spotify'] ?? '';
              legacyArtist['id'] = artistData['id']; // Proper Spotify artist ID
              legacyArtist['spotify_id'] = artistData['id']; // Also store as spotify_id for clarity

              print(
                  '🎵 [Onboarding] Updated artist: ${artist.name} with Spotify ID: ${artistData['id']}, image: ${imageUrl != null ? 'yes' : 'no'}');
            } else {
              // Fallback to track search if artist search fails
              final searchResults = await spotifyService
                  .searchTracks('artist:"${artist.name}"', limit: 1);

              if (searchResults.isNotEmpty) {
                final track = searchResults.first;

                // Update the ArtistWithGenre object
                final updatedArtist = artist.copyWith(
                  imageUrl: track.albumArtUrl ?? track.albumArt,
                  genres: track.genres,
                  popularity: track.popularity,
                  url: track.url,
                  id: track.id, // Use track ID as fallback
                );
                similarArtists[i] = updatedArtist;

                // Update legacy format
                legacyArtist['image_url'] = track.albumArtUrl ?? track.albumArt;
                legacyArtist['genres'] = track.genres;
                legacyArtist['popularity'] = track.popularity;
                legacyArtist['url'] = track.url;
                legacyArtist['id'] = track.id;
                legacyArtist['spotify_id'] = track.id;

                print(
                    '🎵 [Onboarding] Updated artist: ${artist.name} via track search with ID: ${track.id}');
              }
            }

            // Remove this artist from loading state
            setState(() {
              _loadingSimilarArtistImages[artistName]?.remove(artist.name);
            });
          } catch (e) {
            print(
                '❌ [Onboarding] Error searching for artist ${artist.name}: $e');
            // Remove this artist from loading state even if search fails
            setState(() {
              _loadingSimilarArtistImages[artistName]?.remove(artist.name);
            });
          }
        }

        // Update both state systems with enhanced data
        setState(() {
          _similarArtistsMap[artistName] = legacySimilarArtists;

          final newSimilarArtistsMap = Map<String, List<ArtistWithGenre>>.from(
              _artistSelectionState.similarArtistsMap);
          newSimilarArtistsMap[artistName] = similarArtists;
          _artistSelectionState =
              _artistSelectionState.withSimilarArtists(newSimilarArtistsMap);

          // Clear loading state
          final loadingSet = _loadingSimilarArtistImages[artistName];
          if (loadingSet != null) {
            print(
                '🎵 [Onboarding] Clearing loading state for ${loadingSet.length} remaining artists');
            loadingSet.clear();
          }
        });
      } catch (e) {
        print(
            '❌ [Onboarding] Error enhancing similar artist data with Spotify: $e');
        // Clear loading state for all artists if Spotify enhancement fails
        setState(() {
          _loadingSimilarArtistImages[artistName]?.clear();
        });
      }
    } catch (e) {
      print('❌ [Onboarding] Error loading similar artists for $artistName: $e');
      setState(() {
        _loadingSimilarArtists[artistName] = false;
        _loadingSimilarArtistImages[artistName]?.clear();
      });
    }
  }

  Future<void> _loadArtistsForGenres() async {
    if (_selectedGenres.isEmpty || _artistSelectionState.isLoading) {
      print(
          '🎵 [Onboarding] Skipping artist load - genres empty or already loading');
      return;
    }

    print(
        '🎵 [Onboarding] Starting cycling artist load for genres: ${_selectedGenres.join(", ")}');

    // Initialize the artist loader
    _artistLoader = GenreCyclingArtistLoader(
      selectedGenres: _selectedGenres,
      spotifyService: SpotifyService(),
    );

    setState(() {
      _artistSelectionState = _artistSelectionState.loading();
      _isLoadingArtists = true;
    });

    try {
      // Load first batch of artists using cycling approach
      final newState =
          await _artistLoader!.loadNextBatch(_artistSelectionState);

      setState(() {
        _artistSelectionState = newState;
        _isLoadingArtists = false;

        // Update legacy variables for compatibility
        _availableArtists =
            newState.items.map((artist) => artist.toMap()).toList();
        _hasMoreArtists = newState.hasMore;
      });

      print(
          '🎵 [Onboarding] Initial cycling load complete with ${newState.items.length} artists');
    } catch (e) {
      print('Error loading artists: $e');
      setState(() {
        _artistSelectionState = _artistSelectionState
            .withError('Failed to load artists: ${e.toString()}');
        _isLoadingArtists = false;
        _isProcessingGenreSelection = false;
        _hasMoreArtists = false;
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load artists: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );

        // Still show artist prompt even if loading failed, so user doesn't get stuck
        _addAIMessageWithProgression(
            'Having trouble finding artists for those genres. You can still select some from what we have available!');
      }
    }
  }

  Widget _buildSongSelector() {
    // Use filtered songs if search is active, otherwise use all available songs
    final displaySongs =
        _filteredSongs.isNotEmpty ? _filteredSongs : _availableSongs;
    final isSearchActive = _songSearchController.text.isNotEmpty &&
        _songSearchController.text.length >= 3;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              const Icon(
                Icons.music_note,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Pick Your First Song',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).textTheme.headlineSmall?.color,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Search Bar for Songs
          TextField(
            controller: _songSearchController,
            decoration: InputDecoration(
              hintText: 'Search songs or artists...',
              prefixIcon: _isSearchingSongs
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.search),
              suffixIcon: _songSearchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _filteredSongs.clear();
                          _songSearchController.clear();
                          _songSearchDebounceTimer?.cancel();
                          // Re-enable pagination when clearing search
                          _hasMoreSongs = _availableSongs.isNotEmpty;
                          _isLoadingMoreSongs = false;
                        });
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (value) {
              _performSongSearch(value);
            },
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            isSearchActive
                ? 'Search results from Spotify:'
                : 'Choose a song inspired by your favorite artists to create your first musical pin!',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.color
                  ?.withOpacity(0.7),
            ),
          ),

          const SizedBox(height: 20),

          // Song List
          if (_isLoadingSongs)
            Container(
              height: 180,
              alignment: Alignment.center,
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Finding perfect songs for you...'),
                ],
              ),
            )
          else if (displaySongs.isEmpty)
            Container(
              height: 180,
              alignment: Alignment.center,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.music_off,
                    size: 48,
                    color:
                        Theme.of(context).colorScheme.primary.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No songs found',
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context)
                          .textTheme
                          .bodyMedium
                          ?.color
                          ?.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Try a different search term or check your connection.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context)
                          .textTheme
                          .bodyMedium
                          ?.color
                          ?.withOpacity(0.5),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          else
            SizedBox(
              height: 180, // Fixed height for horizontal list
              child: ListView.builder(
                controller: _songScrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                scrollDirection: Axis.horizontal,
                itemCount: displaySongs.length + (_hasMoreSongs ? 1 : 0),
                itemBuilder: (context, index) {
                  // Show loading card at the end if we're loading more or there are more results
                  if (index == displaySongs.length) {
                    if (_isLoadingMoreSongs || _hasMoreSongs) {
                      return _buildLoadingCard();
                    }
                    return const SizedBox.shrink();
                  }
                  final song = displaySongs[index];
                  final isSelected = _selectedTrack?['id'] == song['id'];

                  return Container(
                    width: 150, // Fixed width for each card
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color:
                            isSelected ? AppColors.primary : Colors.transparent,
                        width: 2,
                      ),
                      color: Theme.of(context).cardColor.withOpacity(0.1),
                    ),
                    child: InkWell(
                      onTap: () => _selectSong(song),
                      borderRadius: BorderRadius.circular(12),
                      child: Stack(
                        children: [
                          // Song Cover Image
                          if (song['album_cover'] != null)
                            Positioned.fill(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: Image.network(
                                  song['album_cover'],
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) =>
                                      Container(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .surfaceContainerHighest,
                                    child: Icon(
                                      Icons.music_note,
                                      size: 40,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .primary
                                          .withOpacity(0.5),
                                    ),
                                  ),
                                ),
                              ),
                            ),

                          // Gradient overlay
                          Positioned.fill(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withOpacity(0.7),
                                  ],
                                ),
                              ),
                            ),
                          ),

                          // Song Info
                          Positioned(
                            bottom: 12,
                            left: 12,
                            right: 12,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  song['title'] ?? 'Unknown Title',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  song['artist'] ?? 'Unknown Artist',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.7),
                                    fontSize: 12,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                if (song['album'] != null) ...[
                                  const SizedBox(height: 2),
                                  Text(
                                    song['album'],
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.5),
                                      fontSize: 11,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                                if (song['is_search_result'] == true) ...[
                                  const SizedBox(height: 4),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 6, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: Colors.green.withOpacity(0.3),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Text(
                                      'Search',
                                      style: TextStyle(
                                        color: Colors.green,
                                        fontSize: 10,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),

                          // Selection indicator
                          if (isSelected)
                            Positioned(
                              top: 8,
                              right: 8,
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: const BoxDecoration(
                                  color: AppColors.primary,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

          const SizedBox(height: 16),

          // Selected Song Count
          if (_selectedTrack != null)
            Text(
              'Song selected: ${_selectedTrack!['title']} by ${_selectedTrack!['artist']}',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.7),
              ),
            ),

          const SizedBox(height: 16),

          // Continue Button
          if (_selectedTrack != null)
            ElevatedButton(
              onPressed: () => _handleSongSelection(),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 48),
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Continue to Pin Placement'),
            ),
        ],
      ),
    );
  }

  // Widget _buildInputArea() {
  //   return Container(
  //     padding: const EdgeInsets.all(16),
  //     child: Row(
  //       children: [
  //         Expanded(
  //           child: TextField(
  //             controller: _textController,
  //             decoration: InputDecoration(
  //               hintText: 'Type a message...',
  //               border: OutlineInputBorder(
  //                 borderRadius: BorderRadius.circular(24),
  //               ),
  //               contentPadding: const EdgeInsets.symmetric(
  //                 horizontal: 16,
  //                 vertical: 12,
  //               ),
  //             ),
  //             onSubmitted: (value) => _handleUserInput(value),
  //           ),
  //         ),
  //         const SizedBox(width: 8),
  //         IconButton(
  //           onPressed: () => _handleUserInput(_textController.text),
  //           icon: const Icon(Icons.send),
  //           style: IconButton.styleFrom(
  //             backgroundColor: AppColors.primary,
  //             foregroundColor: Colors.white,
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  bool _shouldShowInput() {
    return _currentStep == OnboardingStep.welcome ||
        _currentStep == OnboardingStep.completion;
  }

  void _handleUserInput(String input) {
    if (input.trim().isEmpty) return;

    _addUserMessage(input);
    _textController.clear();

    // Handle input based on current step
    Timer(const Duration(milliseconds: 500), () {
      _addAIMessage(AIDialogueContent.getRandomMessage(
          AIDialogueContent.postAuthMessages));
    });
  }

  Future<void> _handleAppleMusicSignIn() async {
    if (_isAuthenticating) return;

    setState(() {
      _isAuthenticating = true;
    });

    _addUserMessage('Signing in with Apple...');

    try {
      final result = await _authService.handleAppleSignIn();

      if (result['success'] == true) {
        final userData = result['user'];
        final isNewUser = result['is_new_user'] == true;
        _userEmail = userData['email'] ?? '';

        // Show appropriate message based on whether this is a new or existing user
        if (isNewUser) {
          _addUserMessage('Successfully signed in with Apple!');
          // Complete authentication with AuthProvider for new users
          final authProvider =
              Provider.of<AuthProvider>(context, listen: false);
          final authSuccess = await _authService.completeAuthentication(
            authProvider: authProvider,
            userData: userData,
            authToken: result['auth_token'],
            refreshToken: result['refresh_token'],
          );

          if (authSuccess) {
            Timer(const Duration(milliseconds: 500), () {
              _addAIMessageWithProgression(AIDialogueContent.getRandomMessage(
                  AIDialogueContent.postAuthMessages));
              Timer(const Duration(milliseconds: 1000), () {
                _addAIMessage(AIDialogueContent.getRandomMessage(
                    AIDialogueContent.genrePrompts));
              });
            });
          } else {
            throw Exception('Failed to complete authentication');
          }
        } else {
          _addUserMessage(
              'Successfully signed in with Apple! Welcome back! 🍎');
          // For existing users, complete authentication and go directly to map
          _proceedWithExistingUserAuthentication(result, userData);
        }
      } else {
        throw Exception(result['message'] ?? 'Apple Sign-In failed');
      }
    } catch (e) {
      _addAIMessage(
          AIDialogueContent.getRandomMessage(AIDialogueContent.errorMessages));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Authentication failed: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isAuthenticating = false;
      });
    }
  }

  Future<void> _handleGoogleSignIn() async {
    if (_isAuthenticating) return;

    setState(() {
      _isAuthenticating = true;
    });

    _addUserMessage('Signing in with Google...');

    try {
      final result = await _authService.handleGoogleSignIn();

      if (result['success'] == true) {
        final userData = result['user'];
        final isNewUser = result['is_new_user'] == true;
        _userEmail = userData['email'];

        // Show appropriate message based on whether this is a new or existing user
        if (isNewUser) {
          _addUserMessage('Successfully signed in with Google!');
          // Complete authentication with AuthProvider for new users
          final authProvider =
              Provider.of<AuthProvider>(context, listen: false);
          final authSuccess = await _authService.completeAuthentication(
            authProvider: authProvider,
            userData: userData,
            authToken: result['auth_token'],
            refreshToken: result['refresh_token'],
          );

          if (authSuccess) {
            Timer(const Duration(milliseconds: 500), () {
              _addAIMessageWithProgression(AIDialogueContent.getRandomMessage(
                  AIDialogueContent.postAuthMessages));
              Timer(const Duration(milliseconds: 1000), () {
                _addAIMessage(AIDialogueContent.getRandomMessage(
                    AIDialogueContent.genrePrompts));
              });
            });
          } else {
            throw Exception('Failed to complete authentication');
          }
        } else {
          _addUserMessage(
              'Successfully signed in with Google! Welcome back! 👋');
          // For existing users, complete authentication and go directly to map
          _proceedWithExistingUserAuthentication(result, userData);
        }
      } else {
        throw Exception(result['message'] ?? 'Google Sign-In failed');
      }
    } catch (e) {
      _addAIMessage(
          AIDialogueContent.getRandomMessage(AIDialogueContent.errorMessages));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Authentication failed: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isAuthenticating = false;
      });
    }
  }

  Future<void> _handleEmailSignIn() async {
    if (_isAuthenticating) return;

    // Show email input dialog
    _showEmailSignInDialog();
  }

  void _openTermsOfService() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const TermsOfServiceScreen(),
      ),
    );
  }

  void _openPrivacyPolicy() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PrivacyPolicyScreen(),
      ),
    );
  }

  void _showEmailSignInDialog() {
    final emailController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Sign in with Email'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
                'Enter your email address to receive a verification code:'),
            const SizedBox(height: 16),
            TextField(
              controller: emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: const InputDecoration(
                labelText: 'Email',
                hintText: '<EMAIL>',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _sendVerificationCode(emailController.text),
            child: const Text('Send Code'),
          ),
        ],
      ),
    );
  }

  Future<void> _sendVerificationCode(String email) async {
    if (email.trim().isEmpty) return;

    Navigator.pop(context); // Close email dialog

    setState(() {
      _isAuthenticating = true;
    });

    _addUserMessage('Sending verification code to $email...');

    try {
      final result = await _authService.sendEmailVerificationCode(email);

      if (result['success'] == true) {
        _userEmail = email;
        _addAIMessage(AIDialogueContent.getRandomMessage(
            AIDialogueContent.emailVerificationMessages));
        _showVerificationCodeDialog();
      } else {
        throw Exception(
            result['message'] ?? 'Failed to send verification code');
      }
    } catch (e) {
      _addAIMessage(
          AIDialogueContent.getRandomMessage(AIDialogueContent.errorMessages));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to send code: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isAuthenticating = false;
      });
    }
  }

  void _showVerificationCodeDialog() {
    final codeController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Enter Verification Code'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('We sent a 6-digit code to $_userEmail'),
            const SizedBox(height: 16),
            TextField(
              controller: codeController,
              keyboardType: TextInputType.number,
              maxLength: 6,
              decoration: const InputDecoration(
                labelText: 'Verification Code',
                hintText: '123456',
                border: OutlineInputBorder(),
                counterText: '',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _verifyEmailCode(codeController.text),
            child: const Text('Verify'),
          ),
        ],
      ),
    );
  }

  Future<void> _verifyEmailCode(String code) async {
    if (code.trim().isEmpty) return;

    Navigator.pop(context); // Close verification dialog

    setState(() {
      _isAuthenticating = true;
    });

    _addUserMessage('Verifying code...');

    try {
      final result = await _authService.verifyEmailCode(
        email: _userEmail!,
        code: code,
      );

      if (result['success'] == true) {
        final userData = result['user'];
        final isNewUser = result['is_new_user'] == true;

        // Show appropriate message based on whether this is a new or existing user
        if (isNewUser) {
          _addUserMessage('Email verified! Creating your account...');
          Timer(const Duration(milliseconds: 800), () {
            _addUserMessage('Account created successfully! 🎉');
            _proceedWithAuthentication(result, userData);
          });
        } else {
          _addUserMessage('Email verified! Welcome back! 👋');
          // For existing users, complete authentication and go directly to map
          _proceedWithExistingUserAuthentication(result, userData);
        }
      } else {
        throw Exception(result['message'] ?? 'Email verification failed');
      }
    } catch (e) {
      _addAIMessage(
          AIDialogueContent.getRandomMessage(AIDialogueContent.errorMessages));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Verification failed: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isAuthenticating = false;
      });
    }
  }

  /// Helper method to complete authentication for existing users and navigate to map
  Future<void> _proceedWithExistingUserAuthentication(
      Map<String, dynamic> result, Map<String, dynamic> userData) async {
    try {
      // Validate that we have the required data
      final authToken = result['auth_token']?.toString() ?? '';
      final refreshToken = result['refresh_token']?.toString() ?? '';

      if (authToken.isEmpty) {
        throw Exception('No authentication token received from server');
      }

      if (kDebugMode) {
        print('🔐 [AIOnboarding] Proceeding with existing user authentication');
        print('👤 [AIOnboarding] User: ${userData['email']}');
        print('🔑 [AIOnboarding] Has auth token: ${authToken.isNotEmpty}');
        print(
            '🔄 [AIOnboarding] Has refresh token: ${refreshToken.isNotEmpty}');
      }

      // Complete authentication with AuthProvider
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final authSuccess = await _authService.completeAuthentication(
        authProvider: authProvider,
        userData: userData,
        authToken: authToken,
        refreshToken: refreshToken.isNotEmpty ? refreshToken : null,
      );

      if (authSuccess) {
        if (kDebugMode) {
          print(
              '✅ [AIOnboarding] Existing user authentication completed successfully');
        }

        // Add a brief welcome message
        Timer(const Duration(milliseconds: 500), () {
          _addAIMessage('Welcome back! Taking you to your map... 🗺️');

          // Navigate to map after a short delay
          Timer(const Duration(milliseconds: 1500), () {
            if (mounted) {
              Navigator.of(context).pushReplacementNamed('/map');
            }
          });
        });
      } else {
        throw Exception('Failed to complete authentication');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Existing user authentication error: $e');
      }
      _addAIMessage('There was an issue signing you in. Let\'s try again! 🔄');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Authentication error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isAuthenticating = false;
      });
    }
  }

  /// Helper method to complete authentication after successful verification/registration
  Future<void> _proceedWithAuthentication(
      Map<String, dynamic> result, Map<String, dynamic> userData) async {
    try {
      // Validate that we have the required data
      final authToken = result['auth_token']?.toString() ?? '';
      final refreshToken = result['refresh_token']?.toString() ?? '';

      if (authToken.isEmpty) {
        throw Exception('No authentication token received from server');
      }

      if (kDebugMode) {
        print('🔐 [AIOnboarding] Proceeding with authentication');
        print('👤 [AIOnboarding] User: ${userData['email']}');
        print('🔑 [AIOnboarding] Has auth token: ${authToken.isNotEmpty}');
        print(
            '🔄 [AIOnboarding] Has refresh token: ${refreshToken.isNotEmpty}');
      }

      // Complete authentication with AuthProvider
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final authSuccess = await _authService.completeAuthentication(
        authProvider: authProvider,
        userData: userData,
        authToken: authToken,
        refreshToken: refreshToken.isNotEmpty ? refreshToken : null,
      );

      if (authSuccess) {
        if (kDebugMode) {
          print('✅ [AIOnboarding] Authentication completed successfully');
        }
        Timer(const Duration(milliseconds: 500), () {
          _addAIMessageWithProgression(AIDialogueContent.getRandomMessage(
              AIDialogueContent.postAuthMessages));
          Timer(const Duration(milliseconds: 1000), () {
            _addAIMessage(AIDialogueContent.getRandomMessage(
                AIDialogueContent.genrePrompts));
          });
        });
      } else {
        throw Exception('Failed to complete authentication');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Authentication error: $e');
      }
      _addAIMessage(
          'There was an issue setting up your account. Let\'s try again! 🔄');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Authentication error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleGenreSelection() {
    if (_selectedGenres.isEmpty ||
        _isLoadingArtists ||
        _isProcessingGenreSelection) {
      return;
    }

    print('🎵 [Onboarding] Starting genre selection processing...');
    setState(() {
      _isProcessingGenreSelection = true;
    });

    _addUserMessage('Selected genres: ${_selectedGenres.join(', ')}');

    // Save genres immediately after selection
    _saveMusicPreferences();

    Timer(const Duration(milliseconds: 500), () {
      print('🎵 [Onboarding] Adding genre comment and progressing step...');
      final genreComment = _getGenreComment(_selectedGenres.first);
      _addAIMessageWithProgression(genreComment);

      // Load artists after genre comment
      Timer(const Duration(milliseconds: 1000), () {
        print('🎵 [Onboarding] Starting artist loading...');
        _loadArtistsForGenres().then((_) {
          print(
              '🎵 [Onboarding] Artist loading completed. Showing artist prompt...');
          setState(() {
            _isProcessingGenreSelection = false;
          });
          // Show artist prompt and progress to artist selection after loading completes
          _addAIMessageWithProgression(AIDialogueContent.getRandomMessage(
              AIDialogueContent.artistPrompts));
        });
      });
    });
  }

  String _getGenreComment(String genre) {
    final genreResponses = AIDialogueContent.genreResponses[genre] ??
        AIDialogueContent.multiGenreResponses;
    return AIDialogueContent.getRandomMessage(genreResponses);
  }

  void _selectSong(Map<String, dynamic> song) {
    setState(() {
      _selectedTrack = song;
    });
  }

  void _handleSongSelection() {
    if (_selectedTrack != null) {
      _addUserMessage(
          'Selected song: "${_selectedTrack!['title']}" by ${_selectedTrack!['artist']}');
      Timer(const Duration(milliseconds: 500), () {
        _addAIMessage(
            'Perfect choice! Time to place your first pin on the map! 📍');

        // Navigate to AR pin placement after a short delay
        Timer(const Duration(milliseconds: 1500), () {
          _navigateToARPinPlacement();
        });
      });
    }
  }

  void _handleArtistSelection() {
    _addUserMessage('Selected artists: ${_selectedArtists.join(', ')}');

    // Save artists immediately after selection
    _saveMusicPreferences();

    Timer(const Duration(milliseconds: 500), () {
      _addAIMessageWithProgression(
          AIDialogueContent.getRandomMessage(AIDialogueContent.songPrompts));

      // Load songs after artist selection
      Timer(const Duration(milliseconds: 1000), () {
        _loadSongsForArtists();
      });
    });
  }

  /// Get genres for a given artist by checking local lists first, then Spotify.
  Future<List<String>> _getGenresForArtist(String artistName) async {
    // 1. Check availableArtists
    final localArtist = _availableArtists.firstWhere(
        (a) => a['name']?.toString().toLowerCase() == artistName.toLowerCase(),
        orElse: () => {});
    if (localArtist.isNotEmpty) {
      final genres = (localArtist['genres'] as List?)?.cast<String>() ?? [];
      if (genres.isNotEmpty) return genres;
    }

    // 2. Check similar artists map (and inherit parent's genres)
    for (final entry in _similarArtistsMap.entries) {
      final parentName = entry.key;
      for (final simArtist in entry.value) {
        if (simArtist['name']?.toString().toLowerCase() ==
            artistName.toLowerCase()) {
          // Try genres on similar artist itself
          final genres = (simArtist['genres'] as List?)?.cast<String>() ?? [];
          if (genres.isNotEmpty) return genres;

          // Inherit parent's genres if present
          final parentGenres = await _getGenresForArtist(parentName);
          if (parentGenres.isNotEmpty) return parentGenres;
        }
      }
    }

    // 3. Fallback to Spotify lookup
    try {
      final spotifyData = await SpotifyService().getArtist(artistName);
      final genres = (spotifyData?['genres'] as List?)?.cast<String>() ?? [];
      return genres;
    } catch (_) {
      return [];
    }
  }



  /// Get Spotify ID for a specific artist from available data
  String? _getSpotifyIdForArtist(String artistName) {
    final artistNameLower = artistName.toLowerCase();

    // 1. Check artist selection state items (only for actually selected artists)
    for (final artist in _artistSelectionState.items) {
      if (artist.name.toLowerCase() == artistNameLower) {
        // Only return ID if this artist is actually selected
        final artistKey = SelectionKeyManager.generateArtistKey(artist.name, artist.sourceGenre);
        if (_artistSelectionState.selectedArtistKeys.contains(artistKey)) {
          print('🔍 [Onboarding] Found Spotify ID for selected artist "$artistName": ${artist.id}');
          return artist.id;
        }
      }
    }

    // 2. Check similar artists map (only if we're looking for a selected artist)
    if (_selectedArtists.any((selected) => selected.toLowerCase() == artistNameLower)) {
      for (final entry in _similarArtistsMap.values) {
        for (final artist in entry) {
          if (artist['name']?.toString().toLowerCase() == artistNameLower) {
            final spotifyId = artist['spotify_id'] as String? ?? artist['id'] as String?;
            if (spotifyId != null) {
              print('🔍 [Onboarding] Found Spotify ID for selected artist "$artistName" in similar artists: $spotifyId');
              return spotifyId;
            }
          }
        }
      }
    }

    // 3. Check legacy available artists (only if we're looking for a selected artist)
    if (_selectedArtists.any((selected) => selected.toLowerCase() == artistNameLower)) {
      for (final artist in _availableArtists) {
        if (artist['name']?.toString().toLowerCase() == artistNameLower) {
          final spotifyId = artist['spotify_id'] as String? ?? artist['id'] as String?;
          if (spotifyId != null) {
            print('🔍 [Onboarding] Found Spotify ID for selected artist "$artistName" in available artists: $spotifyId');
            return spotifyId;
          }
        }
      }
    }

    print('🔍 [Onboarding] No Spotify ID found for artist "$artistName"');
    return null;
  }

  /// Save the user's selected genres and artists to the backend
  Future<void> _saveMusicPreferences() async {
    if (_selectedGenres.isEmpty && _selectedArtists.isEmpty) {
      print('🎵 [Onboarding] No music preferences to save');
      return;
    }

    print('🎵 [Onboarding] Saving music preferences...');
    print('🎵 [Onboarding] Genres: ${_selectedGenres.join(', ')}');
    print('🎵 [Onboarding] Artists: ${_selectedArtists.join(', ')}');
    print('🎵 [Onboarding] Total genres: ${_selectedGenres.length}');
    print('🎵 [Onboarding] Total artists: ${_selectedArtists.length}');

    try {
      // Build genres map (still need to fetch these)
      final Map<String, List<String>> artistGenres = {};
      for (final name in _selectedArtists) {
        final genres = await _getGenresForArtist(name);
        if (genres.isNotEmpty) {
          artistGenres[name] = genres;
        }
      }
      
      // Use pre-captured image URLs and Spotify IDs from selection time
      final artistImageUrls = Map<String, String>.from(_selectedArtistImageUrls);
      final artistSpotifyIds = Map<String, String>.from(_selectedArtistSpotifyIds);

      print('🎵 [Onboarding] Artist genres: $artistGenres');
      print('🎵 [Onboarding] Artist image URLs: $artistImageUrls');
      print('🎵 [Onboarding] Artist Spotify IDs: $artistSpotifyIds');

      final result = await _authService.saveMusicPreferences(
        topGenres: _selectedGenres,
        topArtists: _selectedArtists,
        artistGenres: artistGenres,
        artistImageUrls: artistImageUrls,
        artistSpotifyIds: artistSpotifyIds,
      );

      if (result['success'] == true) {
        print('✅ [Onboarding] Music preferences saved successfully');
        print('🎵 [Onboarding] Response data: ${result['data']}');
      } else {
        print(
            '❌ [Onboarding] Failed to save music preferences: ${result['message']}');
        // Don't show error to user as this is not critical for onboarding completion
      }
    } catch (e) {
      print('❌ [Onboarding] Error saving music preferences: $e');
      // Don't show error to user as this is not critical for onboarding completion
    }
  }

  Future<void> _loadSongsForArtists() async {
    if (_selectedArtists.isEmpty) {
      print('🎵 [Onboarding] Skipping song load - no artists selected');
      return;
    }

    print(
        '🎵 [Onboarding] Starting cycling song load for ${_selectedArtists.length} artists');

    // Initialize the song loader
    _songLoader = ArtistCyclingSongLoader(
      selectedArtists: _selectedArtists,
      spotifyService: SpotifyService(),
    );

    setState(() {
      _songSelectionState = _songSelectionState.loading();
      _isLoadingSongs = true;
    });

    try {
      // Load first batch of songs using cycling approach
      final newState = await _songLoader!.loadNextBatch(_songSelectionState);

      setState(() {
        _songSelectionState = newState;
        _isLoadingSongs = false;

        // Update legacy variables for compatibility
        _availableSongs = newState.items
            .map((song) => {
                  'id': song.id,
                  'title': song.title,
                  'artist': song.artist,
                  'album': song.album,
                  'album_cover': song.albumArtUrl ?? song.albumArt,
                  'uri': song.uri,
                  'url': song.url,
                  'duration_ms': song.durationMs,
                  'preview_url': song.previewUrl,
                  'popularity': song.popularity ?? 50,
                })
            .toList();
        _hasMoreSongs = newState.hasMore;
      });

      print(
          '🎵 [Onboarding] Initial cycling song load complete with ${newState.items.length} songs');
    } catch (e) {
      print('Error loading songs: $e');
      setState(() {
        _songSelectionState = _songSelectionState
            .withError('Failed to load songs: ${e.toString()}');
        _isLoadingSongs = false;
        _hasMoreSongs = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load songs: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToARPinPlacement() async {
    if (_selectedTrack == null) return;

    try {
      // Request location permission first
      final locationPermission = await _requestLocationPermission();
      if (!locationPermission) {
        throw Exception('Location permission is required to place pins');
      }

      // Get current location
      final Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      if (kDebugMode) {
        print(
            '📍 [AIOnboarding] Current location: ${position.latitude}, ${position.longitude}');
      }

      // Convert the selected track to MusicTrack format for AR screen
      final musicTrack = MusicTrack(
        id: _selectedTrack!['id'] ?? '',
        title: _selectedTrack!['title'] ?? '',
        artist: _selectedTrack!['artist'] ?? '',
        album: _selectedTrack!['album'] ?? '',
        albumArt: _selectedTrack!['album_cover'] ?? '',
        url: _selectedTrack!['url'] ?? '',
        uri: _selectedTrack!['uri'] ?? '',
        service: 'spotify',
        serviceType: 'spotify',
        durationMs: _selectedTrack!['duration_ms'] ?? 0,
        previewUrl: _selectedTrack!['preview_url'],
        albumArtUrl: _selectedTrack!['album_cover'],
      );

      // Navigate to AR pin placement screen with current location
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ARPinPlacementScreen(
            selectedTrack: musicTrack,
            initialLatitude: position.latitude,
            initialLongitude: position.longitude,
          ),
        ),
      );

      // Handle the result when returning from AR screen
      if (result != null) {
        // Pin was successfully placed
        _handleFirstPinPlaced(result);
      } else {
        // User cancelled or didn't place a pin
        _addAIMessage(
            'No worries! You can always come back to place your first pin later. Let\'s continue setting up your account! 🎵');
        Timer(const Duration(milliseconds: 1000), () {
          _proceedToCompletion();
        });
      }
    } catch (e) {
      print('Error navigating to AR screen: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Settings',
            textColor: Colors.white,
            onPressed: () async {
              await Geolocator.openAppSettings();
            },
          ),
        ),
      );

      // Fallback: proceed to completion
      _addAIMessage(
          'Having trouble with permissions? No problem! You can place pins later from the map. Let\'s finish setting up your account! 📱');
      Timer(const Duration(milliseconds: 1000), () {
        _proceedToCompletion();
      });
    }
  }

  /// Request location permission with proper user messaging
  Future<bool> _requestLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Show dialog explaining why location is needed
      if (!mounted) return false;
      final bool turnOn = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Location Services Required'),
          content:
              const Text('BOPMaps needs location services to place music pins. '
                  'Would you like to turn on location services?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Not Now'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: const Text('Turn On'),
            ),
          ],
        ),
      );

      if (turnOn) {
        await Geolocator.openLocationSettings();
        // Recheck after settings
        serviceEnabled = await Geolocator.isLocationServiceEnabled();
        if (!serviceEnabled) return false;
      } else {
        return false;
      }
    }

    // Check permission status
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      // Show dialog explaining why permission is needed
      if (!mounted) return false;
      final bool requestPermission = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Location Permission Required'),
          content: const Text(
              'BOPMaps needs access to your location to place music pins. '
              'Your location will only be used when you choose to place a pin.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Not Now'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: const Text('Continue'),
            ),
          ],
        ),
      );

      if (!requestPermission) return false;

      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Show dialog explaining how to enable in settings
      if (!mounted) return false;
      final bool openSettings = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Location Permission Required'),
          content:
              const Text('BOPMaps needs location permission to place pins. '
                  'Please enable location permission in your device settings.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Not Now'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: const Text('Open Settings'),
            ),
          ],
        ),
      );

      if (openSettings) {
        await Geolocator.openAppSettings();
        // Recheck after settings
        permission = await Geolocator.checkPermission();
        if (permission == LocationPermission.deniedForever) {
          return false;
        }
      } else {
        return false;
      }
    }

    return true;
  }

  void _handleFirstPinPlaced(dynamic pinData) {
    // Store pin data for celebration
    setState(() {
      _currentStep = OnboardingStep.celebration;
    });

    _addAIMessage(
        AIDialogueContent.getRandomMessage(AIDialogueContent.celebrations));

    // Start confetti celebration
    _confettiController.play();

    // Save music preferences in the background
    _saveMusicPreferences();

    // Show achievement card in chat
    Timer(const Duration(milliseconds: 1000), () {
      _addAchievementCard(
        title: 'First Pin Placed!',
        description:
            'You\'ve placed your first musical pin on the map. Keep exploring and sharing music!',
        xpEarned: 100,
        icon: Icons.place,
      );
    });

    // Show skin unlock message and card
    Timer(const Duration(milliseconds: 2000), () {
      _addAIMessage('🎨 You\'ve unlocked your first pin skin!');
      _addSkinCard(
        name: 'Basement Bopper',
        description:
            'Starting your musical journey from the underground',
        type: 'ARTIST',
        isPremium: false,
        image: 'assets/images/pins/basement_bopper.png',
      );
    });

    // Show map skin unlock message and card
    Timer(const Duration(milliseconds: 3500), () {
      _addAIMessage('🗺️ You\'ve also unlocked your first map skin!');
      _addMapSkinCard(
        name: 'Vaporwave',
        description:
            'A retro-futuristic aesthetic with neon grids and digital sunsets',
        type: 'MAP',
        isPremium: false,
        image: 'assets/images/map_skin/vaporwave.png',
      );
    });

    // Show continue button
    Timer(const Duration(milliseconds: 4500), () {
      _addContinueButton();
    });
  }

  void _addAchievementCard(
      {required String title,
      required String description,
      required int xpEarned,
      required IconData icon}) {
    setState(() {
      _messages.add(ChatMessage(
        message: '',
        isAI: true,
        timestamp: DateTime.now(),
        specialContent: Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primary.withOpacity(0.3),
                  AppColors.primary.withOpacity(0.1),
                  Colors.purple.withOpacity(0.2),
                ],
              ),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          icon,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Achievement Unlocked!',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.white.withOpacity(0.7),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              title,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '+$xpEarned XP',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ));
    });
    _scrollToBottom();
  }

  void _addSkinCard({
    required String name,
    required String description,
    required String type,
    required bool isPremium,
    required String image,
  }) {
    setState(() {
      _messages.add(ChatMessage(
        message: '',
        isAI: true,
        timestamp: DateTime.now(),
        specialContent: Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue.withOpacity(0.4),
                  Colors.indigo.withOpacity(0.2),
                  Colors.blue.withOpacity(0.1),
                ],
              ),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.2),
                  blurRadius: 15,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // Pin preview with glow
                      Stack(
                        children: [
                          // Glow effect
                          Container(
                            width: 72,
                            height: 72,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.blue.withOpacity(0.5),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                          ),
                          // Pin image
                          SizedBox(
                            width: 72,
                            height: 72,
                            child: ClipOval(
                              child: Image.asset(
                                image,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'New Pin Skin!',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.white.withOpacity(0.7),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.purple.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.purple.withOpacity(0.4),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                type,
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.purple,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ));
    });
    _scrollToBottom();
  }

  void _addMapSkinCard({
    required String name,
    required String description,
    required String type,
    required bool isPremium,
    required String image,
  }) {
    setState(() {
      _messages.add(ChatMessage(
        message: '',
        isAI: true,
        timestamp: DateTime.now(),
        specialContent: Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.green.withOpacity(0.4),
                  Colors.teal.withOpacity(0.2),
                  Colors.green.withOpacity(0.1),
                ],
              ),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.2),
                  blurRadius: 15,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // Map preview with glow
                      Stack(
                        children: [
                          // Glow effect
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.green.withOpacity(0.5),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                          ),
                          // Map image
                          SizedBox(
                            width: 72,
                            height: 72,
                            child: ClipOval(
                              child: Image.asset(
                                image,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'New Map Skin!',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.white.withOpacity(0.7),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.green.withOpacity(0.4),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                type,
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ));
    });
    _scrollToBottom();
  }

  void _addContinueButton() {
    setState(() {
      _messages.add(ChatMessage(
        message: '',
        isAI: true,
        timestamp: DateTime.now(),
        specialContent: Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: ElevatedButton(
            onPressed: () {
              _navigateToMap();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Continue to Map',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(width: 8),
                Icon(Icons.arrow_forward),
              ],
            ),
          ),
        ),
      ));
    });
    _scrollToBottom();
  }

  void _proceedToCompletion() {
    setState(() {
      _currentStep = OnboardingStep.completion;
    });

    _addAIMessage(
        AIDialogueContent.getRandomMessage(AIDialogueContent.finalMessages));

    // Save music preferences in the background
    _saveMusicPreferences();

    // Add continue button
    Timer(const Duration(milliseconds: 1500), () {
      _addContinueButton();
    });
  }

  /// Perform artist search with debounce and Spotify integration
  void _performArtistSearch(String query) {
    // Cancel previous timer
    _searchDebounceTimer?.cancel();

    // Clear results if query is empty
    if (query.trim().isEmpty) {
      setState(() {
        _filteredArtists.clear();
        _isSearchingArtists = false;
      });
      return;
    }

    // Check minimum character requirement
    if (query.trim().length < 3) {
      setState(() {
        _filteredArtists.clear();
        _isSearchingArtists = false;
      });
      return;
    }

    // Set loading state and reset pagination for search
    setState(() {
      _isSearchingArtists = true;
      _filteredArtists.clear();
      // Reset pagination state for search results
      _artistsOffset = 0;
      _hasMoreArtists = false; // Disable pagination for search results
      _isLoadingMoreArtists = false;
    });

    // Debounce the search
    _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () async {
      await _searchArtistsOnSpotify(query.trim());
    });
  }

  /// Search for artists on Spotify
  Future<void> _searchArtistsOnSpotify(String query) async {
    try {
      print('🎵 [Onboarding] Searching Spotify for artists: "$query"');

      final spotifyService = SpotifyService();

      // Use the new searchArtists method for direct artist search
      final searchResults =
          await spotifyService.searchArtists(query, limit: 20);

      // Mark all results as search results
      final searchArtists = searchResults
          .map((artist) => {
                ...artist,
                'is_search_result': true, // Mark as search result
              })
          .toList();

      print(
          '🎵 [Onboarding] Found ${searchArtists.length} artists for query: "$query"');

      if (mounted) {
        setState(() {
          _filteredArtists = searchArtists;
          _isSearchingArtists = false;
        });

        // Scroll to beginning when search results are populated
        if (_artistScrollController.hasClients && searchArtists.isNotEmpty) {
          _artistScrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      }
    } catch (e) {
      print('❌ [Onboarding] Error searching artists on Spotify: $e');
      if (mounted) {
        setState(() {
          _filteredArtists.clear();
          _isSearchingArtists = false;
        });
      }
    }
  }

  void _performSongSearch(String query) {
    // Cancel previous timer
    _songSearchDebounceTimer?.cancel();

    // Clear results if query is empty
    if (query.trim().isEmpty) {
      setState(() {
        _filteredSongs.clear();
        _isSearchingSongs = false;
      });
      return;
    }

    // Check minimum character requirement
    if (query.trim().length < 3) {
      setState(() {
        _filteredSongs.clear();
        _isSearchingSongs = false;
      });
      return;
    }

    // Set loading state and reset pagination for search
    setState(() {
      _isSearchingSongs = true;
      _filteredSongs.clear();
      // Reset pagination state for search results
      _songsOffset = 0;
      _hasMoreSongs = false; // Disable pagination for search results
      _isLoadingMoreSongs = false;
    });

    // Debounce the search
    _songSearchDebounceTimer =
        Timer(const Duration(milliseconds: 500), () async {
      await _searchSongsOnSpotify(query.trim());
    });
  }

  /// Search for songs on Spotify
  Future<void> _searchSongsOnSpotify(String query) async {
    try {
      print('🎵 [Onboarding] Searching Spotify for songs: "$query"');

      final spotifyService = SpotifyService();

      // Use the searchTracks method for direct song search
      final searchResults = await spotifyService.searchTracks(query, limit: 20);

      // Convert to song format and mark as search results
      final searchSongs = searchResults
          .map((track) => {
                'id': track.id,
                'title': track.title,
                'artist': track.artist,
                'album': track.album,
                'album_cover': track.albumArtUrl ?? track.albumArt,
                'uri': track.uri,
                'url': track.url,
                'duration_ms': track.durationMs,
                'preview_url': track.previewUrl,
                'is_search_result': true, // Mark as search result
              })
          .toList();

      print(
          '🎵 [Onboarding] Found ${searchSongs.length} songs for query: "$query"');

      if (mounted) {
        setState(() {
          _filteredSongs = searchSongs;
          _isSearchingSongs = false;
        });

        // Scroll to beginning when search results are populated
        if (_songScrollController.hasClients && searchSongs.isNotEmpty) {
          _songScrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      }
    } catch (e) {
      print('❌ [Onboarding] Error searching songs on Spotify: $e');
      if (mounted) {
        setState(() {
          _filteredSongs.clear();
          _isSearchingSongs = false;
        });
      }
    }
  }

  Widget _buildModernArtistLoadingState() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated Music Discovery Icon
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 2000),  
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: 0.8 + (0.2 * (0.5 + 0.5 * (1 + math.sin(value * 2 * math.pi)))),
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primary.withOpacity(0.8),
                        AppColors.primary.withOpacity(0.4),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Rotating outer ring
                      RotationTransition(
                        turns: AlwaysStoppedAnimation(value),
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 2,
                            ),
                          ),
                        ),
                      ),
                      // Center music icon
                      const Icon(
                        Icons.music_note_rounded,
                        color: Colors.white,
                        size: 40,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(height: 32),
          
          // Dynamic loading text that cycles through messages
          StreamBuilder<int>(
            stream: Stream.periodic(const Duration(milliseconds: 2000), (i) => i),
            builder: (context, snapshot) {
              final loadingTexts = [
                'Analyzing your music taste...',
                'Finding amazing artists for you...',
                'Discovering hidden gems...',
                'Curating perfect matches...',
              ];
              final currentIndex = (snapshot.data ?? 0) % loadingTexts.length;
              
              return AnimatedSwitcher(
                duration: const Duration(milliseconds: 500),
                child: Text(
                  loadingTexts[currentIndex],
                  key: ValueKey(currentIndex),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                    letterSpacing: -0.3,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Animated progress indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(4, (index) {
              return TweenAnimationBuilder<double>(
                duration: Duration(milliseconds: 800 + (index * 200)),
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  // Create a pulsing effect
                  final pulseValue = 0.3 + (0.7 * (0.5 + 0.5 * math.sin(value * 2 * math.pi + index * 0.5)));
                  
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(pulseValue),
                      shape: BoxShape.circle,
                    ),
                  );
                },
              );
            }),
          ),
          
          const SizedBox(height: 24),
          
          // Subtle hint text
          Text(
            'This might take a moment while we explore millions of tracks',
            style: TextStyle(
              fontSize: 13,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// Data Classes
class ChatMessage {
  final String message;
  final bool isAI;
  final DateTime timestamp;
  final Widget? specialContent; // Added for achievement and skin cards

  ChatMessage({
    required this.message,
    required this.isAI,
    required this.timestamp,
    this.specialContent, // Initialize specialContent
  });
}

enum OnboardingStep {
  welcome,
  auth,
  genres,
  genreComment,
  artists,
  songSelection,
  pinPlacement,
  celebration,
  completion,
}
